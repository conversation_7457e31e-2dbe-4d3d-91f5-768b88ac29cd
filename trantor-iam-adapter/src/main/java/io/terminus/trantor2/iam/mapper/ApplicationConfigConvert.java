package io.terminus.trantor2.iam.mapper;

import io.terminus.iam.api.request.application.ApplicationConfigParams;
import io.terminus.iam.api.response.application.ApplicationConfigResult;
import io.terminus.trantor2.iam.dto.ApplicationConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR> qianjin
 * @since : 2024/1/12
 */
@Mapper(componentModel = "spring")
public interface ApplicationConfigConvert {


    @Mappings(value = {
        @Mapping(target = "id", ignore = true),
        @Mapping(target = "application", ignore = true),
        @Mapping(target = "noticeSms", ignore = true),
        @Mapping(target = "noticeEmail", ignore = true),
        @Mapping(target = "emailPrefix", ignore = true),
    })
    ApplicationConfig convert(ApplicationConfigResult applicationConfigResult);

    @Mappings(value = {
        @Mapping(target = "emailPrefix", ignore = true),
    })
    ApplicationConfigParams convert(ApplicationConfig applicationConfig);
}
