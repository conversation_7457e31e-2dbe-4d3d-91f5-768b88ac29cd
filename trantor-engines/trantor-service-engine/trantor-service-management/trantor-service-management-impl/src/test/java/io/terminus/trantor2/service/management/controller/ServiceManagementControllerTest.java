package io.terminus.trantor2.service.management.controller;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.utils.BusinessSnowFlake;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.module.service.ModuleManageService;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.enums.HttpMethod;
import io.terminus.trantor2.service.engine.ServiceEngine;
import io.terminus.trantor2.service.engine.ai.configuration.AiProperties;
import io.terminus.trantor2.service.engine.impl.component.ModelMetaQuery;
import io.terminus.trantor2.service.management.api.template.ServiceTemplateApi;
import io.terminus.trantor2.service.management.converter.ServiceMetaConverter;
import io.terminus.trantor2.service.management.converter.ServiceTemplateMetaConverter;
import io.terminus.trantor2.service.management.model.po.ServicePO;
import io.terminus.trantor2.service.management.model.request.HttpReportRequest;
import io.terminus.trantor2.service.management.parser.RenameVariableParser;
import io.terminus.trantor2.service.management.parser.ServiceVariableParser;
import io.terminus.trantor2.service.management.service.ServiceDefinitionGeneratorService;
import io.terminus.trantor2.service.management.service.ServiceMetaService;
import io.terminus.trantor2.service.management.service.ServiceSchedulerJobService;
import io.terminus.trantor2.service.management.service.impl.ServiceTempService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ServiceManagementControllerTest
 *
 * <AUTHOR> Created on 2024/9/14 09:57
 */
class ServiceManagementControllerTest {

    @Mock
    private ServiceVariableParser serviceVariableParser;

    @Mock
    private ServiceMetaService serviceMetaService;
    @Mock
    private ServiceTemplateApi serviceTemplateApi;
    @Mock
    private ServiceTemplateMetaConverter serviceTemplateMetaConverter;
    @Mock
    private BusinessSnowFlake businessSnowFlake;
    @Mock
    private ServiceDefinitionGeneratorService serviceDefinitionGeneratorService;
    @Mock
    private ModuleManageService moduleService;
    @Mock
    private AiProperties aiProperties;
    @Mock
    private ServiceTempService serviceTempService;
    @Mock
    private ServiceSchedulerJobService serviceSchedulerJobService;
    @Mock
    private TeamService teamService;
    @Mock
    TrantorIAMUserService trantorIAMUserService;
    @Mock
    ModelMetaQuery modelMetaQuery;
    @Mock
    RenameVariableParser renameVariableParser;

    private ServiceMetaConverter serviceMetaConverter;
    @Mock
    private ServiceManagementController serviceManagementController;
    private ServiceEngine serviceEngine;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        serviceEngine = new ServiceEngine();
        serviceEngine.setTeamService(teamService);
        serviceMetaConverter = new ServiceMetaConverter(trantorIAMUserService, modelMetaQuery);

//        serviceManagementController = new ServiceManagementController(serviceEngine,
//                serviceVariableParser,
//                renameVariableParser,
//                serviceMetaConverter,
//                serviceMetaService,
//                null,
//                serviceTemplateApi,
//                serviceTemplateMetaConverter,
//                businessSnowFlake,
//                serviceDefinitionGeneratorService,
//                moduleService,
//                aiProperties,
//                serviceTempService,
//                serviceSchedulerJobService);
    }

    @Test
    void httpReport() {
        // request
        HttpReportRequest request = new HttpReportRequest();
        request.setModuleKey("testModuleKey");
        request.setServiceKey("testServiceKey");
        request.setUrl("http://test.url");
        request.setMethod(HttpMethod.GET);
        request.setAccessLevel(MetaNodeAccessLevel.Private);
        request.setResponseType(FieldType.Object);
        request.setParentKey("testParentKey");

        // 模拟数据库的数据
        ServiceDefinition originServiceDsl = new ServiceDefinition("testServiceKey", "testServiceKey");
        originServiceDsl.getProps().setPermissionKey("testPermissionKey");
        ServicePO originService = new ServicePO();
        originService.setServiceDslJson(originServiceDsl);
        originService.setParentKey("testModuleKey$testParentKey");
        when(serviceMetaService.findByServiceKey(any(), any())).thenReturn(Optional.of(originService));

        // 模拟生成的dsl
        when(serviceDefinitionGeneratorService.generateServiceDefinition(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(new ServiceDefinition("testServiceKey", "testServiceKey"));

        when(teamService.getTeamIdByCode(any())).thenReturn(1L);

        // Act
        Response<Boolean> response = serviceManagementController.httpReport(request);

        // 验证保存的时候，permissionKey是否被设置成数据库的值
        ArgumentCaptor<ServicePO> servicePOCaptor = ArgumentCaptor.forClass(ServicePO.class);
        verify(serviceMetaService).save(servicePOCaptor.capture());
        ServicePO updatedServicePO = servicePOCaptor.getValue();

        assertEquals("testPermissionKey", updatedServicePO.getServiceDslJson().getProps().getPermissionKey());

        System.out.println(updatedServicePO.getServiceDslJson().getProps().getPermissionKey());
    }
}
