package io.terminus.trantor2.service.engine.ai.llm.tool.execution;

import com.openai.models.chat.completions.ChatCompletionTool;
import io.terminus.trantor2.service.common.exception.ServiceException;
import io.terminus.trantor2.service.dsl.enums.ToolType;
import io.terminus.trantor2.service.dsl.properties.HttpTool;
import io.terminus.trantor2.service.dsl.properties.McpTool;
import io.terminus.trantor2.service.dsl.properties.ServiceTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.engine.ai.llm.util.ToolCallUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 工具执行器工厂类
 * 实现工厂方法模式，根据工具类型创建对应的执行器
 */
@Component
@Slf4j
public class ToolExecutionFactory {
    private final Map<ToolType, ToolCallExecution> executionMap;

    public ToolExecutionFactory(List<ToolCallExecution> executions) {
        this.executionMap = new HashMap<>();
        for (ToolCallExecution execution : executions) {
            ToolType toolType = getToolType(execution);
            executionMap.put(toolType, execution);
            log.info("Registered tool execution: {} for tool type: {}", execution.getClass().getSimpleName(), toolType);
        }
    }

    public ChatCompletionTool buildServiceTool(ServiceTool tool) {
        ToolCallExecution execution = createExecution(tool);
        return execution.buildTool(tool);
    }

    public List<ChatCompletionTool> buildMcpTool(McpTool tool) {
        McpToolExecution execution = (McpToolExecution) createExecution(tool);
        return execution.buildTools(tool);
    }

    public ChatCompletionTool buildHttpTool(HttpTool tool) {
        ToolCallExecution execution = createExecution(tool);
        return execution.buildTool(tool);
    }

    /**
     * 根据工具类型创建对应的执行器
     *
     * @param tool 工具定义
     * @return 工具执行器
     */
    public ToolCallExecution createExecution(SkillTool tool) {
        ToolCallExecution execution = executionMap.get(tool.getType());
        if (execution == null) {
            throw new UnsupportedOperationException("Unsupported tool type: " + tool.getType());
        }
        return execution;
    }

    /**
     * 根据执行器实例获取对应的工具类型
     *
     * @param execution 执行器实例
     * @return 工具类型
     */
    private ToolType getToolType(ToolCallExecution execution) {
        if (execution instanceof ServiceToolExecution) {
            return ToolType.Service;
        }
        if (execution instanceof HttpToolExecution) {
            return ToolType.Http;
        }
        if (execution instanceof McpToolExecution) {
            return ToolType.Mcp;
        }
        throw new IllegalArgumentException("Unknown execution type: " + execution.getClass().getSimpleName());
    }

    public String invokeTool(List<SkillTool> tools, String toolName, String toolArguments) {
        for (SkillTool tool : tools) {
            if (isToolMatch(tool, toolName)) {
                return invokeTool(tool, toolName, toolArguments);
            }
        }
        log.warn("未找到匹配的工具: toolName=[{}]", toolName);
        return "";
    }

    public String invokeTool(SkillTool tool, String toolName, String toolArguments) {
        try {
            ToolCallExecution execution = createExecution(tool);
            return execution.execute(tool, toolName, toolArguments);
        } catch (Exception e) {
            log.error("调用工具失败: tool=[{}], toolName=[{}], error={}", tool.getClass().getSimpleName(), toolName, e.getMessage(), e);
            if (e instanceof ServiceException serviceException) {
                if (Objects.nonNull(serviceException.getInnerMsg())) {
                    return serviceException.getInnerMsg();
                } else {
                    return e.getMessage();
                }
            }
            return e.getMessage();
        }
    }

    /**
     * 判断工具是否匹配
     */
    private boolean isToolMatch(SkillTool tool, String toolName) {
        if (tool instanceof ServiceTool serviceTool) {
            // 编排服务的工具Key是带有模块前缀的，统一使用 __{moduleKey}__{toolName} 格式的名
            return ToolCallUtil.getServiceKeyInLlmToolName(serviceTool.getKey()).equals(toolName);
        } else if (tool instanceof McpTool mcpTool) {
            // MCP工具需要检查子工具列表
            return mcpTool.getTools().stream().anyMatch(subTool -> subTool.getName().equals(toolName));
        } else if (tool instanceof HttpTool httpTool) {
            return httpTool.getKey().equals(toolName);
        }
        return false;
    }
}
