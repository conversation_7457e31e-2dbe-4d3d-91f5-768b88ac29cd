package io.terminus.trantor2.service.engine.ai.llm;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.core.http.Headers;
import com.openai.core.http.StreamResponse;
import com.openai.models.audio.AudioResponseFormat;
import com.openai.models.audio.transcriptions.Transcription;
import com.openai.models.audio.transcriptions.TranscriptionCreateParams;
import com.openai.models.audio.transcriptions.TranscriptionCreateResponse;
import com.openai.models.chat.completions.ChatCompletion;
import com.openai.models.chat.completions.ChatCompletionChunk;
import com.openai.models.chat.completions.ChatCompletionCreateParams;
import com.openai.models.chat.completions.ChatCompletionStreamOptions;
import com.openai.models.chat.completions.ChatCompletionTool;
import com.openai.models.embeddings.CreateEmbeddingResponse;
import com.openai.models.embeddings.EmbeddingCreateParams;
import com.openai.models.images.Image;
import com.openai.models.images.ImageGenerateParams;
import com.openai.models.images.ImagesResponse;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.service.dsl.properties.HttpTool;
import io.terminus.trantor2.service.dsl.properties.McpTool;
import io.terminus.trantor2.service.dsl.properties.ServiceTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.engine.ai.configuration.AiProperties;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import io.terminus.trantor2.service.engine.ai.core.chat.session.ChatSession;
import io.terminus.trantor2.service.engine.ai.core.message.AiMessage;
import io.terminus.trantor2.service.engine.ai.core.message.ErrorContent;
import io.terminus.trantor2.service.engine.ai.core.message.TokenContent;
import io.terminus.trantor2.service.engine.ai.core.message.Usage;
import io.terminus.trantor2.service.engine.ai.llm.tool.LlmToolCallHandler;
import io.terminus.trantor2.service.engine.ai.llm.tool.LlmToolCallMetadata;
import io.terminus.trantor2.service.engine.ai.llm.tool.execution.ToolExecutionFactory;
import io.terminus.trantor2.service.engine.ai.llm.util.ToolCallUtil;
import io.terminus.trantor2.service.engine.ai.platform.utils.AIUtil;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.FileInputStream;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
@AllArgsConstructor
public class LlmClientService {
    private final AiProperties aiProperties;
    private final LlmResponseHandler llmResponseHandler;
    private final LlmToolCallHandler llmToolCallHandler;
    private final ToolExecutionFactory toolExecutionFactory;

    // @formatter:off
    private final Cache<String, OpenAIClient> cache = CacheBuilder.newBuilder()
                                                                  .maximumSize(100)
                                                                  .expireAfterAccess(Duration.ofHours(1))
                                                                  .build();
    // @formatter:on

    private OpenAIClient getAIClientFromCache(String modelProvider, String modelName, String source) {
        StringBuilder key = new StringBuilder();
        User user = null;
        // 拼接当前用户信息，先从Trantor上下文中取，取不到则从AI上下文中取
        if (TrantorContext.safeGetCurrentUser().isPresent()) {
            user = TrantorContext.safeGetCurrentUser().get();
            key.append(String.format("%d#", TrantorContext.getCurrentUserId()));
        } else {
            if (AIContext.safeGetContext().isPresent()) {
                user = AIContext.safeGetContext().get().getUser();
                if (user != null) {
                    key.append(String.format("%d#", user.getId()));
                } else {
                    Long userId = AIContext.safeGetContext().get().getUserId();
                    if (userId != null) {
                        key.append(String.format("%d#", userId));
                    } else {
                        log.warn("current user is anonymous or user is null");
                    }
                }
            }
        }
        key.append(String.format("(%s)$(%s)@(%s)", modelProvider, modelName, source));
        log.info("ai client cache key: {}", key);

        OpenAIClient client = null;
        try {
            client = cache.getIfPresent(key.toString());
            // 缓存未命中则创建AI客户端对象
            if (client == null) {
                if (aiProperties.isEnable()) {
                    if (aiProperties.getAiProxy() != null) {
                        String apiKey = aiProperties.getAiProxy().getAccessKeyId();
                        client = buildClient(modelProvider, apiKey, user, source);
                        cache.put(key.toString(), client);
                    } else {
                        log.error("ai-proxy properties not found");
                    }
                } else {
                    log.warn("ai not enable");
                }
            }
        } catch (Exception e) {
            log.error("load ai client in cache error", e);
        }
        return client;
    }

    private OpenAIClient buildClient(String modelProvider, String apiKey, User user, String source) {
        String baseUrl = String.format("%s/v1", aiProperties.getAiProxy().getDomain());
        Map<String, String> customHeaders = AIUtil.buildHttpAuditHeader(user, modelProvider, source);
        Headers.Builder headersBuilder = Headers.builder();
        customHeaders.forEach(headersBuilder::put);

        // @formatter:off
        return OpenAIOkHttpClient.builder()
                                 .baseUrl(baseUrl)
                                 .apiKey(apiKey)
                                 .headers(headersBuilder.build())
                                 .maxRetries(AIUtil.DEFAULT_MAX_RETRIES)
                                 .timeout(AIUtil.DEFAULT_TIMEOUT)
                                 .build();
        // @formatter:on
    }

    /**
     * embedding
     *
     * @param data 输入数据
     * @return embedding结果
     */
    public List<Float> embedding(String modelProvider, String modelName, String data) {
        OpenAIClient client = getAIClientFromCache(modelProvider, modelName, null);
        EmbeddingCreateParams createParams = EmbeddingCreateParams.builder()
                .model(modelName)
                .input(data)
                .build();
        CreateEmbeddingResponse embeddingResponse = client.embeddings().create(createParams);
        return embeddingResponse.data().get(0).embedding();
    }

    /**
     * 使用默认配置进行对话完成并返回指定类型的结果
     *
     * @param userMessage  用户消息
     * @param responseType 返回类型
     * @param <T>          返回类型
     * @return 指定类型的结果
     */
    public <T> T chatCompletion(String userMessage, Class<T> responseType) {
        validateUserMessage(userMessage);
        validateResponseType(responseType);
        log.debug("Starting chat completion with default settings, responseType: {}", responseType.getSimpleName());
        return chatCompletion(AIUtil.DEFAULT_MODEL_PROVIDER, AIUtil.DEFAULT_MODEL_NAME, AIUtil.DEFAULT_TEMPERATURE, AIUtil.DEFAULT_MAX_TOKENS, userMessage, responseType);
    }

    /**
     * 使用指定配置进行对话完成并返回指定类型的结果
     *
     * @param modelProvider 提供商类型
     * @param modelName     模型名称
     * @param temperature   温度
     * @param maxTokens     最大令牌数
     * @param userMessage   用户消息
     * @param responseType  返回类型
     * @param <T>           返回类型
     * @return 指定类型的结果
     */
    public <T> T chatCompletion(String modelProvider, String modelName, Double temperature, Integer maxTokens, String userMessage, Class<T> responseType) {
        // 参数校验和默认值设置
        modelProvider = validateProviderType(modelProvider);
        modelName = validateModelName(modelName);
        temperature = validateTemperature(temperature);
        maxTokens = validateMaxTokens(maxTokens);
        validateUserMessage(userMessage);
        validateResponseType(responseType);

        log.debug("Building chat completion params with provider: {}, model: {}, temperature: {}, maxTokens: {}", modelProvider, modelName, temperature, maxTokens);

        ChatCompletionCreateParams params = buildChatCompletionCreateParams(modelName, userMessage, temperature, maxTokens, false);
        return chatCompletion(modelProvider, modelName, params, responseType);
    }

    /**
     * 使用指定请求进行对话完成并返回指定类型的结果
     *
     * @param modelProvider 提供商类型
     * @param modelName     模型名称
     * @param params        请求参数
     * @param responseType  返回类型
     * @param <T>           返回类型
     * @return 指定类型的结果
     */
    public <T> T chatCompletion(String modelProvider, String modelName, ChatCompletionCreateParams params, Class<T> responseType) {
        return chatCompletion(modelProvider, modelName, params, responseType, this.getClass().getName());
    }

    /**
     * 使用指定请求进行对话完成并返回指定类型的结果
     *
     * @param modelProvider 提供商类型
     * @param modelName     模型名称
     * @param params        请求参数
     * @param responseType  返回类型
     * @param auditSource   请求来源（用于日志审计）
     * @param <T>           返回类型
     * @return 指定类型的结果
     */
    public <T> T chatCompletion(String modelProvider, String modelName, ChatCompletionCreateParams params, Class<T> responseType, String auditSource) {
        // 参数校验
        modelProvider = validateProviderType(modelProvider);
        modelName = validateModelName(modelName);
        validateChatCompletionCreateParams(params);
        validateResponseType(responseType);

        log.debug("Executing chat completion with provider: {}, model: {}", modelProvider, modelName);

        try {
            OpenAIClient client = getAIClientFromCache(modelProvider, modelName, auditSource);
            ChatCompletion chatCompletion = client.chat().completions().create(params);
            return AIUtil.buildMessageByChunk(chatCompletion, responseType, false, null, new StringBuilder());
        } catch (Exception e) {
            log.error("Failed to complete chat params: {}", e.getMessage(), e);

            if (AiMessage.class.isAssignableFrom(responseType)) {
                try {
                    ErrorContent errorContent = new ErrorContent();
                    errorContent.setDetails(e.getMessage());
                    AiMessage aiMessage = AIUtil.buildAiMessage(errorContent);
                    return (T) aiMessage;
                } catch (Exception ex) {
                    log.error("Failed to create error message: {}", ex.getMessage(), ex);
                }
            }

            // 重新抛出异常，让调用者处理
            throw new RuntimeException("Failed to complete AI params", e);
        }
    }

    /**
     * 使用默认配置进行流式对话并返回SSE
     *
     * @param userMessage  用户消息
     * @param responseType 返回类型
     * @param <T>          返回类型
     * @return SSE
     */
    public <T> String chatCompletionStream(String userMessage, Class<T> responseType) {
        validateUserMessage(userMessage);
        validateResponseType(responseType);
        log.debug("Starting chat completion stream with default settings, responseType: {}", responseType.getSimpleName());
        return chatCompletionStream(AIUtil.DEFAULT_MODEL_PROVIDER, AIUtil.DEFAULT_MODEL_NAME, AIUtil.DEFAULT_TEMPERATURE, AIUtil.DEFAULT_MAX_TOKENS, userMessage, responseType);
    }

    /**
     * 使用指定配置进行流式对话并返回SSE
     *
     * @param modelProvider 提供商类型
     * @param modelName     模型名称
     * @param temperature   温度
     * @param maxTokens     最大令牌数
     * @param userMessage   用户消息
     * @param responseType  返回类型
     * @param <T>           返回类型
     * @return SSE
     */
    public <T> String chatCompletionStream(String modelProvider, String modelName, Double temperature, Integer maxTokens, String userMessage, Class<T> responseType) {
        // 参数校验和默认值设置
        modelProvider = validateProviderType(modelProvider);
        modelName = validateModelName(modelName);
        temperature = validateTemperature(temperature);
        maxTokens = validateMaxTokens(maxTokens);
        validateUserMessage(userMessage);
        validateResponseType(responseType);

        log.debug("Building streaming chat params with provider: {}, model: {}, temperature: {}, maxTokens: {}", modelProvider, modelName, temperature, maxTokens);

        ChatCompletionCreateParams params = buildChatCompletionCreateParams(modelName, userMessage, temperature, maxTokens, true);
        return chatCompletionStream(modelProvider, modelName, params, responseType);
    }

    /**
     * 流式返回AI响应结果，支持自定义返回类型
     *
     * @param modelProvider 提供商类型
     * @param modelName     模型名称
     * @param params        请求参数
     * @param responseType  返回类型的Class
     * @param <T>           返回类型
     * @return SSE
     */
    public <T> String chatCompletionStream(String modelProvider, String modelName, ChatCompletionCreateParams params, Class<T> responseType) {
        return chatCompletionStream(modelProvider, modelName, params, responseType, this.getClass().getName());
    }

    /**
     * 流式返回AI响应结果，支持自定义返回类型
     *
     * @param modelProvider 提供商类型
     * @param modelName     模型名称
     * @param params        请求参数
     * @param responseType  返回类型的Class
     * @param auditSource   请求来源（用于日志审计）
     * @param <T>           返回类型
     * @return SSE
     */
    public <T> String chatCompletionStream(String modelProvider, String modelName, ChatCompletionCreateParams params,
                                           Class<T> responseType, String auditSource) {
        return chatCompletionStream(modelProvider, modelName, params, responseType, auditSource, null);
    }

    public <T> String chatCompletionStream(String modelProvider, String modelName, ChatCompletionCreateParams params,
                                           Class<T> responseType, String auditSource, ChatSession chatSession) {
        return chatCompletionStream(modelProvider, modelName, params, responseType, auditSource, chatSession, true);
    }

    public <T> String chatCompletionStream(String modelProvider, String modelName, ChatCompletionCreateParams params,
                                           Class<T> responseType, String auditSource, ChatSession chatSession,
                                           boolean streamOutput) {
        // 参数校验和默认值设置
        modelProvider = validateProviderType(modelProvider);
        modelName = validateModelName(modelName);
        validateChatCompletionCreateParams(params);
        validateResponseType(responseType);

        log.debug("Starting chat completion stream with provider: {}, model: {}", modelProvider, modelName);
        log.info("Executing chat completion stream params: {}", params);

        SseEmitter sseEmitter = getSseEmitter();

        OpenAIClient client = getAIClientFromCache(modelProvider, modelName, auditSource);

        StringBuilder finalOutput = new StringBuilder();
        List<LlmToolCallMetadata> toolCallsMetadata = new ArrayList<>();

        // 在异步线程模型中使用上下文
        TrantorContext.Context trantorContext = TrantorContext.copy();
        AIContext.Context aiContext = AIContext.copy();
        // 针对编排服务设置的运行上下文对象
        VariableContext programmableExecutionContext = AIContext.getContext().getVariableContext();

        if (params.tools().isPresent()) {
            ChatCompletionCreateParams.Builder builder = params.toBuilder();
            client.async()
                    .chat()
                    .completions()
                    .createStreaming(params)
                    .subscribe(chunk -> {
                        setupContexts(trantorContext, aiContext, programmableExecutionContext);

                        // 调用llm决策要调用哪些FunctionTool，并生成FunctionTool调用参数
                        llmResponseHandler.handleResponse(chatSession, chunk, finalOutput, toolCallsMetadata);
                    })
                    .onCompleteFuture()
                    .whenComplete((unused, throwable) -> {
                        setupContexts(trantorContext, aiContext, programmableExecutionContext);

                        if (!toolCallsMetadata.isEmpty()) {
                            // 将FunctionTool调用结果重新构造回上下文
                            llmToolCallHandler.attachToolCallMessage(chatSession, builder, toolCallsMetadata, auditSource);
                        }
                    })
                    .thenAccept(unused -> {
                        setupContexts(trantorContext, aiContext, programmableExecutionContext);

                        LlmToolCallMetadata toolCallMetadata = ToolCallUtil.findFinalOutputToolMetadata(toolCallsMetadata);
                        if (Objects.nonNull(toolCallMetadata)) {
                            String toolCallOutput = toolCallMetadata.getToolOutput();
                            AIUtil.sendSseMessageWithResponseType(toolCallOutput, chatSession, sseEmitter, responseType, finalOutput, streamOutput);

                            log.info("[{}] invoke tool final output: {}", auditSource, finalOutput);
                        } else {
                            // 结合FunctionTool调用结果的上下文重新进行llm调用获得最终结果
                            try (StreamResponse<ChatCompletionChunk> streamResponse = client.chat().completions().createStreaming(builder.build())) {
                                streamResponse.stream().forEach(chunk -> {
                                    llmResponseHandler.handleChunk(chatSession, chunk, sseEmitter, responseType, finalOutput, streamOutput);
                                });
                            } catch (Exception e) {
                                log.error("llm response error: {}", e.getMessage(), e);
                                AIUtil.sendSseMessageWithError(sseEmitter, e);
                            }

                            log.info("[{}] invoke llm with tool final output: {}", auditSource, finalOutput);
                        }

                        if (Objects.nonNull(chatSession)) {
                            // 多轮对话记录
                            chatSession.close();
                        }
                    })
                    .join();
        } else {
            try (StreamResponse<ChatCompletionChunk> streamResponse = client.chat().completions().createStreaming(params)) {
                streamResponse.stream().forEach(chunk -> {
                    llmResponseHandler.handleChunk(chatSession, chunk, sseEmitter, responseType, finalOutput, streamOutput);
                });
            } catch (Exception e) {
                log.error("llm response error: {}", e.getMessage(), e);
                chatSession.onError(e);
                AIUtil.sendSseMessageWithError(sseEmitter, e);
            }

            log.info("[{}] invoke llm final output: {}", auditSource, finalOutput);

            if (Objects.nonNull(chatSession)) {
                // 多轮对话记录
                chatSession.close();
            }
        }

        return finalOutput.toString();
    }

    /**
     * 构建ChatCompletionCreateParams
     *
     * @param modelName   模型名称
     * @param userMessage 用户消息
     * @param temperature 温度
     * @param maxTokens   最大令牌数
     * @param isStream    是否流式请求
     * @return ChatCompletionCreateParams
     */
    public ChatCompletionCreateParams buildChatCompletionCreateParams(String modelName,
                                                                      String userMessage,
                                                                      Double temperature,
                                                                      Integer maxTokens,
                                                                      boolean isStream) {
        // @formatter:off
        ChatCompletionCreateParams.Builder builder = ChatCompletionCreateParams.builder()
                                                                               .model(modelName)
                                                                               .addUserMessage(userMessage)
                                                                               .maxCompletionTokens(maxTokens)
                                                                               .temperature(temperature);
        if (isStream) {
            builder.streamOptions(ChatCompletionStreamOptions.builder().includeUsage(true).build());
        }
        // @formatter:on

        return builder.build();
    }

    public <T> List<Image> imagesGenerate(String modelProvider, String modelName, ImageGenerateParams params, Class<T> responseType, String auditSource) {
        SseEmitter sseEmitter = getSseEmitter();

        List<Image> finalOutput = new ArrayList<>();
        OpenAIClient client = getAIClientFromCache(modelProvider, modelName, auditSource);
        ImagesResponse imagesResponse = client.images().generate(params);
        if (imagesResponse.data().isPresent()) {
            try {
                finalOutput = imagesResponse.data().get();
                sseEmitter.send(imagesResponse.data().get());
                if (aiProperties.isSseMessageBodyStructured()) {
                    if (imagesResponse.usage().isPresent()) {
                        // @formatter:off
                        Usage tokenUsage = Usage.builder()
                                                .inputTokens((int) imagesResponse.usage().get().inputTokens())
                                                .outputTokens((int) imagesResponse.usage().get().outputTokens())
                                                .totalTokens((int) imagesResponse.usage().get().totalTokens())
                                                .build();
                        // @formatter:on
                        TokenContent content = new TokenContent();
                        content.setUsage(tokenUsage);
                        AiMessage tokenMessage = AIUtil.buildAiMessage(content);
                        sseEmitter.send(SseEmitter.event().data(tokenMessage));
                    }
                }
            } catch (IOException e) {
                log.error("send sse message error: {}", e.getMessage(), e);
                AIUtil.sendSseMessageWithError(sseEmitter, e);
            }
        }
        return finalOutput;
    }

    public ImageGenerateParams buildImageGenerateParams(String modelName, String userMessage, String responseFormat, String quality, String size) {
        // @formatter:off
        return ImageGenerateParams.builder()
                                  .n(1)
                                  .model(modelName)
                                  .prompt(userMessage)
                                  .responseFormat(ImageGenerateParams.ResponseFormat.of(responseFormat))
                                  .quality(ImageGenerateParams.Quality.of(quality))
                                  .size(ImageGenerateParams.Size.of(size))
                                  .build();
        // @formatter:on
    }

    public <T> Transcription audioTranscriptionStream(String modelProvider, String modelName, TranscriptionCreateParams params, Class<T> responseType, String auditSource) {
        SseEmitter sseEmitter = getSseEmitter();

        OpenAIClient client = getAIClientFromCache(modelProvider, modelName, auditSource);
        /*
        try (StreamResponse<TranscriptionStreamEvent> streamResponse = client.audio().transcriptions().createStreaming(params)) {
            streamResponse.stream().forEach(event -> {
                if (event.transcriptTextDelta().isPresent()) {
                    try {
                        sseEmitter.send(event.asTranscriptTextDelta().delta());
                    } catch (IOException e) {
                        log.error("send sse message error: {}", e.getMessage(), e);
                        AIUtil.sendSseMessageWithError(sseEmitter, e);
                    }
                }
            });
        }
        */
        client.withOptions(builder -> builder.putHeader("X-AI-PROXY-MODEL-ID", "9d318b51-5ce5-4f53-8a1e-c94544bf72e4"));
        TranscriptionCreateResponse transcriptionResponse = client.audio().transcriptions().create(params);
        if (transcriptionResponse.transcription().isPresent()) {
            return transcriptionResponse.transcription().get();
        }
        return null;
    }

    @SneakyThrows
    public TranscriptionCreateParams buildAudioTranscriptionParams(String modelName, String userMessage, String responseFormat, String audioPath) {
        // @formatter:off
        return TranscriptionCreateParams.builder()
                                        .model(modelName)
                                        .prompt(userMessage)
                                        .file(new FileInputStream(audioPath))
                                        .responseFormat(AudioResponseFormat.JSON)
                                        .build();
        // @formatter:on
    }

    public List<ChatCompletionTool> buildChatCompletionTools(List<SkillTool> tools) {
        List<ChatCompletionTool> chatCompletionTools = new ArrayList<>();
        tools.forEach(tool -> {
            if (tool instanceof ServiceTool serviceTool) {
                ChatCompletionTool chatCompletionTool = toolExecutionFactory.buildServiceTool(serviceTool);
                chatCompletionTools.add(chatCompletionTool);
            } else if (tool instanceof McpTool mcpTool) {
                chatCompletionTools.addAll(toolExecutionFactory.buildMcpTool(mcpTool));
            } else if (tool instanceof HttpTool httpTool) {
                ChatCompletionTool chatCompletionTool = toolExecutionFactory.buildHttpTool(httpTool);
                chatCompletionTools.add(chatCompletionTool);
            }
        });
        return chatCompletionTools;
    }

    public SseEmitter getSseEmitter() {
        SseEmitter sseEmitter;
        // AI线程上下文环境中是否有SSE，如果有则复用
        if (Objects.nonNull(AIContext.getContext()) && Objects.nonNull(AIContext.getContext().getGlobalSseEmitter())) {
            sseEmitter = AIContext.getContext().getGlobalSseEmitter();
        } else {
            // 设置超时时间为3分钟
            sseEmitter = new SseEmitter(Duration.ofHours(AIUtil.DEFAULT_SSE_TIMEOUT_HOURS).toMillis());
        }
        return sseEmitter;
    }

    /**
     * 验证提供商类型
     *
     * @param modelProvider 提供商类型
     * @return 验证后的提供商类型
     */
    private String validateProviderType(String modelProvider) {
        if (StringUtils.isBlank(modelProvider)) {
            log.warn("Model provider is blank");
            return "";
        }
        return modelProvider;
    }

    /**
     * 验证模型名称
     *
     * @param modelName 模型名称
     * @return 验证后的模型名称
     */
    private String validateModelName(String modelName) {
        if (StringUtils.isBlank(modelName)) {
            log.warn("Model name is blank, using default: {}", AIUtil.DEFAULT_MODEL_NAME);
            return AIUtil.DEFAULT_MODEL_NAME;
        }
        return modelName;
    }

    /**
     * 验证温度参数
     *
     * @param temperature 温度参数
     * @return 验证后的温度参数
     */
    private Double validateTemperature(Double temperature) {
        if (temperature == null) {
            log.warn("Temperature is null, using default: {}", AIUtil.DEFAULT_TEMPERATURE);
            return AIUtil.DEFAULT_TEMPERATURE;
        }
        if (temperature < 0 || temperature > 1) {
            log.warn("Temperature {} is outside valid range [0.0,1.0], using default: {}", temperature, AIUtil.DEFAULT_TEMPERATURE);
            return AIUtil.DEFAULT_TEMPERATURE;
        }
        return temperature;
    }

    /**
     * 验证最大令牌数
     *
     * @param maxTokens 最大令牌数
     * @return 验证后的最大令牌数
     */
    private Integer validateMaxTokens(Integer maxTokens) {
        if (maxTokens == null || maxTokens <= 0) {
            log.warn("MaxTokens is invalid, using default: {}", AIUtil.DEFAULT_MAX_TOKENS);
            return AIUtil.DEFAULT_MAX_TOKENS;
        }
        return maxTokens;
    }

    /**
     * 验证用户消息
     *
     * @param userMessage 用户消息
     * @param <T>         返回类型
     * @throws IllegalArgumentException 参数不合法时抛出
     */
    private <T> void validateUserMessage(String userMessage) {
        if (StringUtils.isBlank(userMessage)) {
            log.warn("user message is blank or null");
            throw new IllegalArgumentException("User message cannot be empty");
        }
    }

    /**
     * 验证ChatCompletionCreateParams请求
     *
     * @param params ChatCompletionCreateParams请求
     * @param <T>    返回类型
     * @throws IllegalArgumentException 参数不合法时抛出
     */
    private <T> void validateChatCompletionCreateParams(ChatCompletionCreateParams params) {
        if (params == null) {
            log.warn("chat completion params is null");
            throw new IllegalArgumentException("ChatCompletionCreateParams cannot be null");
        }
    }

    /**
     * 验证响应类型
     *
     * @param responseType 响应类型
     * @param <T>          返回类型
     * @throws IllegalArgumentException 参数不合法时抛出
     */
    private <T> void validateResponseType(Class<T> responseType) {
        if (responseType == null) {
            log.warn("Response type is null");
            throw new IllegalArgumentException("Response type cannot be null");
        }
    }

    public void setupContexts(TrantorContext.Context trantorContext, AIContext.Context aiContext, VariableContext programmableExecutionContext) {
        if (Objects.isNull(TrantorContext.getContext())) {
            TrantorContext.init();
            TrantorContext.setContext(trantorContext);
        }
        if (AIContext.safeGetContext().isEmpty()) {
            AIContext.init();
            AIContext.setContext(aiContext);
            AIContext.getContext().setVariableContext(programmableExecutionContext);
        }
    }
}
