package io.terminus.trantor2.service.engine.ai.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.terminus.trantor2.service.engine.ai.llm.tool.LlmToolCallFieldSchema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
public class ProprietaryToolDefinition implements Serializable {
    private static final long serialVersionUID = -4282837749271248373L;

    /**
     * 工具名称
     */
    @JsonProperty("tool_name")
    private String toolName;

    /**
     * 工具描述
     */
    private String description;

    /**
     * 工具模式定义
     */
    @JsonProperty("tool_schema")
    private ToolSchema toolSchema;

    /**
     * 实现细节
     */
    private Implementation implementation;

    /**
     * 元数据信息
     */
    private Metadata metadata;

    /**
     * 设置元数据信息
     *
     * @param author   作者名称
     * @param authorId 作者ID
     */
    public void setMetadataInfo(String author, Long authorId) {
        if (Objects.isNull(this.metadata)) {
            this.metadata = new Metadata();
        }
        this.metadata.setAuthor(author);
        this.metadata.setAuthorId(authorId);
        this.metadata.setCreatedAt(new Date());
    }

    /**
     * 工具模式定义
     */
    @Data
    public static class ToolSchema implements Serializable {
        private static final long serialVersionUID = 7563535845618217095L;

        /**
         * 数据类型
         */
        private String type;

        /**
         * 属性定义
         */
        private Map<String, LlmToolCallFieldSchema> properties;

        /**
         * 必需字段
         */
        private List<String> required;

    }

    /**
     * 属性定义
     */
    @Data
    public static class Properties implements Serializable {
        private static final long serialVersionUID = 1141911768728343498L;

        /**
         * 文件路径属性
         */
        @JsonProperty("file_path")
        private PropertyDefinition filePath;

        /**
         * 试运行属性
         */
        @JsonProperty("dry_run")
        private PropertyDefinition dryRun;

    }

    /**
     * 属性定义详情
     */
    @Data
    public static class PropertyDefinition implements Serializable {
        private static final long serialVersionUID = -9130094473689503712L;

        /**
         * 数据类型
         */
        private String type;

        /**
         * 描述信息
         */
        private String description;

    }

    /**
     * 实现细节
     */
    @Data
    public static class Implementation implements Serializable {
        private static final long serialVersionUID = -4468432535054832656L;

        /**
         * 编程语言
         */
        private String language = "python";

        /**
         * 编码方式
         */
        private String encoding = "base64";

        /**
         * 代码内容
         */
        private String code;

        /**
         * 入口点
         */
        private String entrypoint;

        /**
         * 依赖要求
         */
        private List<String> requirements;
    }

    /**
     * 元数据信息
     */
    @Data
    public static class Metadata implements Serializable {
        private static final long serialVersionUID = -4324424531229210547L;

        /**
         * 版本号
         */
        private String version;

        /**
         * 作者
         */
        private String author;

        /**
         * 作者ID
         */
        private Long authorId;

        /**
         * 创建时间
         */
        private Date createdAt;
    }
}
