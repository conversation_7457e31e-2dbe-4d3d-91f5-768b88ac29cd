package io.terminus.trantor2.service.engine.ai.platform;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.common.consts.AIConst;
import io.terminus.trantor2.service.common.utils.TrantorSseEmitter;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.AIRoundsStrategy;
import io.terminus.trantor2.service.engine.ai.configuration.AiProperties;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import io.terminus.trantor2.service.engine.ai.context.ChatContext;
import io.terminus.trantor2.service.engine.ai.platform.utils.AIUtil;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.exception.ServiceExecuteException;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AIChatLifeCycle extends AbstractAILifeCycle {
    private static final String AI_SERVICE_KEY_SUMMARY = "ai_app_build$AI_Summarize_Service";
    private static final String AI_SERVICE_KEY_SUMMARY_CHAT_CONTENT = "t_ai_management$AI_Summarize_Chat_Content";

    private final AIRepositorySupport aiRepositorySupport;
    private final ServiceExecutor serviceExecutor;
    private final AiProperties aiProperties;

    private final ExecutorService pool = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r);
        thread.setName("ai-chat-lifecycle");
        return thread;
    });

    public Map<String, Object> chatBefore(Long teamId, String serviceKey, Map<String, Object> params, ServiceDefinition definition) {
        Map<String, Object> result = Maps.newHashMap();

        // strategy
        AIRoundsStrategy strategy = Objects.nonNull(definition.getAiRoundsStrategy())
                                  ? definition.getAiRoundsStrategy()
                                  : AIRoundsStrategy.NONE;

        // needRounds
        boolean needRounds = definition.isAiService() && definition.isAiChatMode();
        if (strategy.equals(AIRoundsStrategy.NONE)) {
            log.info("current ai chat unnecessary rounds where execute [{}] service", serviceKey);
            // 当不开启多轮对话策略时则无需进行多轮
            needRounds = false;
        }
        result.put("needRounds", needRounds);

        // userContent
        String userContent;
        if (needRounds) {
            if (params.containsKey("userContent")) {
                userContent = Objects.toString(params.get("userContent"), "");
                result.put("userContent", userContent);
            } else {
                throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_PARAM_NULL, new Object[]{"userContent"});
            }
        } else {
            // 针对没有开启Chat模式的配置默认不进行多轮对话，不对userContent参数进行校验
            userContent = "";
        }

        // sessionId
        String sessionId;
        if (params.containsKey("sessionId")) {
            sessionId = Objects.toString(params.get("sessionId"), "");
            result.put("sessionId", sessionId);
        } else {
            throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_PARAM_NULL, new Object[]{"sessionId"});
        }

        // applicationScene
        long applicationScene;
        if (needRounds) {
            if (params.containsKey("applicationScene")) {
                applicationScene = Long.parseLong(Objects.toString(params.get("applicationScene")));
                result.put("applicationScene", applicationScene);
            } else {
                throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_PARAM_NULL, new Object[]{"applicationScene"});
            }
        } else {
            // 一般只有在请求t-ai接口并且配置错误的情况下设置该值，确保下游的保存逻辑时有值
            applicationScene = 1L;
        }

        // attachments
        List<Object> attachments;
        if (params.containsKey("attachments")) {
            if (params.get("attachments") instanceof List) {
                attachments = (List<Object>) params.get("attachments");
            } else {
                attachments = JsonUtil.fromJson(Objects.toString(params.get("attachments"), ""), new TypeReference<List<Object>>() {});
            }
            result.put("attachments", attachments);
        }

        Long historyId = getHistoryId(teamId, sessionId);
        boolean newChatStatus = false;
        if (Objects.isNull(historyId)) {
            newChatStatus = true;
            // 当新对话时，先截取用户内容作为标题，再一轮聊天结束后再总结标题
            String title = userContent;
            if (title.length() > 100) {
                title = title.substring(0, 100);
            }
            SessionHistory history = saveSessionHistory(teamId, applicationScene, sessionId, title);
            historyId = history.getId();
        }
        result.put("historyId", historyId);
        result.put("newChatStatus", newChatStatus);

        if (needRounds) {
            // 如果需要多轮对话，则直接先清空确保数据安全
            ChatContext.clear();
            ChatContext.init();
            ChatContext.getContext().setTeamId(teamId);
            ChatContext.getContext().setHistoryId(historyId);
            ChatContext.getContext().setStrategy(strategy.name());
            ChatContext.getContext().setNewChatStatus(newChatStatus);
        }
        return result;
    }

    public String chatReady(ServiceDefinition definition, Long teamId, Long historyId, Object params, String userContent) {
        if (Objects.nonNull(definition.getAiRoundsStrategy()) && definition.getAiRoundsStrategy().equals(AIRoundsStrategy.RE_INPUT)) {
            // 只针对Re-Input的多轮次对话策略才需要查询历史总结内容并进行输入重构
            if (existHistorySummary(teamId, historyId)) {
                // 根据sessionId获取对话总结内容用于本次重构输入的内容
                return getCurrSummaryContent(teamId, AI_SERVICE_KEY_SUMMARY_CHAT_CONTENT, params);
            } else {
                return userContent;
            }
        } else {
            return userContent;
        }
    }

    public void chatting(AtomicReference<TrantorSseEmitter> globalSseEmitterRef, TrantorContext.Context trantorContext,
                         AIContext.Context aiContext, ChatContext.Context chatContext, Supplier<Object> serviceSupplier) {
        CompletableFuture.runAsync(() -> {
            // 异步线程切换后上下文也一并带上
            initContext(aiContext, trantorContext, "invoke ServiceExecutor", "chatting");
            if (AIContext.getContext() != null) {
                AIContext.getContext().setGlobalSseEmitter(globalSseEmitterRef.get());
            }
            ChatContext.init();
            ChatContext.setContext(chatContext);

            try {
                Object result = serviceSupplier.get();
                log.info("chat service response: [{}]", result);
                if (aiProperties.isSseMessageBodyStructured()) {
                    globalSseEmitterRef.get().send(SseEmitter.event().data(AIUtil.SSE_MESSAGE_DONE));
                }
                globalSseEmitterRef.get().complete();
            } catch (Exception e) {
                throwException(globalSseEmitterRef.get(), e);
            } finally {
                // 待整个编排服务执行完毕，返回SSE内容后，再清理多轮对话专用的线程上下文
                ChatContext.clear();
            }
        }).thenRun(() -> {
            if (aiProperties.isAsyncContextClean()) {
                cleanContext();
            }
        });
    }

    public void chatDone(AIContext.Context aiContext, TrantorContext.Context trantorContext, Boolean newChatStatus,
                         Long historyId, String sessionId, String serviceKey, String requestContent, String currRoundsContent,
                         Map<String, String> events, Long applicationScene, Long teamId, String eventType, Object params,
                         List<Object> attachments, ServiceDefinition definition) {
        // 对话结束后，全局SSE已关闭并触发onCompletion事件，此时会进行标题总结/输入重构，避免在该阶段进入AI节点时全局SSE不可用，使用柔性实例化
        AIContext.getContext().setGlobalSseEmitter(new TrantorSseEmitter());
        // 摘要当前的对话
        if (newChatStatus) {
            CompletableFuture.runAsync(() -> {
                initContext(aiContext, trantorContext, "update session history title", "chat done");
                updateSessionHistoryTitle(teamId, historyId, sessionId, applicationScene, params);
            }).thenRun(() -> {
                if (aiProperties.isAsyncContextClean()) {
                    cleanContext();
                }
            });
        }
        CompletableFuture.runAsync(() -> {
            initContext(aiContext, trantorContext, "save session history record with session level", "chat done");
            saveSessionHistoryRecordWithSessionLevel(teamId, historyId, serviceKey, eventType, requestContent, attachments, events);
        }).thenRun(() -> {
            if (aiProperties.isAsyncContextClean()) {
                cleanContext();
            }
        });

        if (Objects.nonNull(definition.getAiRoundsStrategy()) && definition.getAiRoundsStrategy().equals(AIRoundsStrategy.RE_INPUT)) {
            if (newChatStatus) {
                saveSessionHistoryRecordSummary(teamId, historyId, requestContent, events);
            } else {
                saveSessionHistoryRecordSummary(teamId, historyId, currRoundsContent, events);
            }
        }
        if (aiProperties.isAsyncContextClean()) {
            cleanContext();
        }
    }

    public void throwException(SseEmitter sseEmitter, Exception e) {
        try {
            log.error("ai chat appear error", e);
            if (e instanceof BusinessException) {
                sseEmitter.send(TrantorSseEmitter.event().name("errorEvent").data(((BusinessException) e).getErrorMsg()));
            } else {
                sseEmitter.send(TrantorSseEmitter.event().name("errorEvent").data("可能AI开了小差，请重新再试"));
            }
            sseEmitter.send(SseEmitter.event().data(AIUtil.SSE_MESSAGE_DONE));
            sseEmitter.complete();
        } catch (Exception exception) {
            log.error("publish error failed", exception);
        }
    }

    private SessionHistory saveSessionHistory(Long teamId, Long applicationSceneId, String sessionId, String title) {
        SessionHistory history = new SessionHistory();
        history.setSessionId(sessionId);
        history.setTitle(title);
        history.setApplicationScene(applicationSceneId);
        history.setCreatedAt(new Date());
        history.setCreatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(-1L));
        history.setUpdatedAt(new Date());
        history.setUpdatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(-1L));
        Object result = aiRepositorySupport.executeSaveDataService(teamId, AIConst.AI_MANAGEMENT_HISTORY_MODEL_KEY, history);
        if (result instanceof Map) {
            history = JsonUtil.NON_INDENT.fromJson(JsonUtil.NON_INDENT.toJson(result), SessionHistory.class);
        }
        return history;
    }

    private void updateSessionHistoryTitle(Long teamId, Long historyId, String sessionId, Long applicationScene, Object params) {
        // TODO 当调用AI总结异常时需要有重试机制进行重试
        String title = getCurrSummaryContent(teamId, AI_SERVICE_KEY_SUMMARY, params);

        if (StringUtils.isNotBlank(title)) {
            SessionHistory history = new SessionHistory();
            history.setId(historyId);
            history.setSessionId(sessionId);
            history.setTitle(title);
            history.setApplicationScene(applicationScene);
            history.setUpdatedAt(new Date());
            history.setUpdatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(-1L));
            aiRepositorySupport.executeSaveDataService(teamId, AIConst.AI_MANAGEMENT_HISTORY_MODEL_KEY, history);
        } else {
            log.info("current summary conversation title was empty");
        }
    }

    private void saveSessionHistoryRecordWithSessionLevel(Long teamId, Long historyId, String source, String eventType,
                                                          String requestContent, List<Object> attachments, Map<String, String> events) {
        // 记录当前Session级对话内容
        SessionHistoryRecord historyRecord = new SessionHistoryRecord();
        historyRecord.setRequestContent(requestContent);
        historyRecord.setType(RecordSourceType.SESSION.getValue());

        List<ReplyEventData> replyEventData = new ArrayList<>();
        if (events != null && !events.isEmpty()) {
            log.info("current transparent event type is {}", eventType);
            events.forEach((eventName, data) -> replyEventData.add(new ReplyEventData(eventName, data)));
        }
        historyRecord.setReplyEventData(JsonUtil.toJson(replyEventData));
        historyRecord.setAttachments(JsonUtil.toJson(attachments));

        historyRecord.setSource(source);
        historyRecord.setTAiSessionHistoryId(historyId);
        historyRecord.setCreatedAt(new Date());
        historyRecord.setCreatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(-1L));
        historyRecord.setUpdatedAt(new Date());
        historyRecord.setUpdatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(-1L));
        aiRepositorySupport.executeSaveDataService(teamId, AIConst.AI_MANAGEMENT_HISTORY_RECORD_MODEL_KEY, historyRecord);
    }

    /**
     * 将当前回答内容以及上一次总结内容进行重新总结并记录到总结表中
     *
     * @param teamId
     * @param historyId
     * @param requestContent
     * @param events
     */
    private void saveSessionHistoryRecordSummary(Long teamId, Long historyId, String requestContent, Map<String, String> events) {
        SessionHistoryRecordSummary historyRecordSummary = new SessionHistoryRecordSummary();
        historyRecordSummary.setRequestContent(requestContent);
        if (events != null && !events.isEmpty()) {
            if (events.keySet().size() > 1) {
                log.warn("current conversation summary was double response");
                // 只取一条作为总结的结果
                for (Map.Entry<String, String> entry : events.entrySet()) {
                    if (StringUtils.isBlank(entry.getKey()) || "text".equals(entry.getKey())) {
                        historyRecordSummary.setReplyContent(entry.getValue());
                    }
                    break;
                }
            } else {
                String data = events.entrySet().stream().findAny().get().getValue();
                historyRecordSummary.setReplyContent(data);
            }
        }
        historyRecordSummary.setTAiSessionHistoryId(historyId);
        historyRecordSummary.setCreatedAt(new Date());
        historyRecordSummary.setCreatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(-1L));
        historyRecordSummary.setUpdatedAt(new Date());
        historyRecordSummary.setUpdatedBy(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(-1L));
        aiRepositorySupport.executeSaveDataService(teamId, AIConst.AI_MANAGEMENT_HISTORY_RECORD_SUMMARY_MODEL_KEY, historyRecordSummary);
    }

    private Long getHistoryId(Long teamId, String sessionId) {
        // 根据sessionId获取历史对话关联Id
        Long historyId = null;
        Map<String, Object> condition = Maps.newHashMap();
        condition.put("sessionId", sessionId);
        Map<String, Object> result = aiRepositorySupport.executeFindOne(teamId,
                                                                        AIConst.AI_MANAGEMENT_HISTORY_MODEL_KEY,
                                                                        Lists.newArrayList("id"),
                                                                        condition);
        if (result != null && result.containsKey("id")) {
            historyId = (Long) result.get("id");
        }
        return historyId;
    }

    private Boolean existHistorySummary(Long teamId, Long historyId) {
        Map<String, Object> condition = Maps.newHashMap();
        condition.put("tAiSessionHistoryId", historyId);
        Map<String, Object> result = aiRepositorySupport.executeFindOne(teamId,
                                                                        AIConst.AI_MANAGEMENT_HISTORY_RECORD_SUMMARY_MODEL_KEY,
                                                                        Lists.newArrayList("id"),
                                                                        condition);
        return result != null && !result.isEmpty();
    }

    public String getCurrSummaryContent(Long teamId, String serviceKey, Object params) {
        Object result = serviceExecutor.execute(serviceKey, Arguments.of(teamId, params));
        StringBuilder contentBuilder = new StringBuilder();
        if (result instanceof String) {
            contentBuilder.append(result);
        } else {
            appendResult(result, contentBuilder);
        }
        return contentBuilder.toString();
    }

    private void publishSseEmitter(TrantorSseEmitter sseEmitter,String eventType, Object result) throws IOException {
        StringBuilder contentBuilder = new StringBuilder();
        appendResult(result, contentBuilder);
        sseEmitter.append(contentBuilder.toString());
        // \n为整个SSE结束休止符，需要对内容中涉及到的全部进行替换，并进行data块切片重构
        String[] fragments = contentBuilder.toString().split("\n\n");
        for (String fragment : fragments) {
            String[] subFragments = fragment.split("\n");
            for (String subFragment : subFragments) {
                sseEmitter.send(TrantorSseEmitter.event().name(eventType).data(subFragment));
            }
            // 与前端约定当有event名作为参数传递进来时，不输出换行（此时内容需要保证为JSON格式，否则纯文本内容会导致应该换行的地方丢失）
            if (StringUtils.isBlank(eventType)) {
                sseEmitter.send("\n");
            }
        }
        sseEmitter.complete();
    }

    private void appendResult(Object result, StringBuilder contentBuilder) {
        if (result instanceof Map && ((Map<?, ?>) result).size() == 1) {
            Object eventData = ((Map<?, ?>) result).values().iterator().next();
            if (eventData instanceof String) {
                contentBuilder.append(eventData);
            } else {
                contentBuilder.append(JsonUtil.toJson(eventData));
            }
        } else if (result instanceof String) {
            contentBuilder.append(result);
        } else {
            contentBuilder.append(JsonUtil.toJson(result));
        }
    }
}
