package io.terminus.trantor2.service.engine.ai.llm.tool;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class LlmToolCallFieldSchema implements Serializable {
    private static final long serialVersionUID = 6656415779562692338L;

    private String type;
    private String title;
    private String description;
    @JsonProperty("default")
    private Object defaultValue;
    private Map<String, LlmToolCallFieldSchema> properties;
    private List<String> required;
    private Boolean additionalProperties;
    private LlmToolCallFieldSchema items;
}
