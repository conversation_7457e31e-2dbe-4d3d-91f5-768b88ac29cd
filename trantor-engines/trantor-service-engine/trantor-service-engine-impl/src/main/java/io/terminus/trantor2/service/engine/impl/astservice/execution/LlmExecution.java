package io.terminus.trantor2.service.engine.impl.astservice.execution;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.openai.models.audio.transcriptions.TranscriptionCreateParams;
import com.openai.models.chat.completions.ChatCompletionContentPart;
import com.openai.models.chat.completions.ChatCompletionContentPartImage;
import com.openai.models.chat.completions.ChatCompletionContentPartText;
import com.openai.models.chat.completions.ChatCompletionCreateParams;
import com.openai.models.chat.completions.ChatCompletionMessageParam;
import com.openai.models.chat.completions.ChatCompletionStreamOptions;
import com.openai.models.chat.completions.ChatCompletionSystemMessageParam;
import com.openai.models.chat.completions.ChatCompletionTool;
import com.openai.models.images.ImageGenerateParams;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.common.consts.AIConst;
import io.terminus.trantor2.service.common.utils.Placeholder;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.enums.LlmModelType;
import io.terminus.trantor2.service.dsl.properties.AIProperties;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.dsl.properties.StringEntry;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import io.terminus.trantor2.service.engine.ai.core.chat.session.ChatSession;
import io.terminus.trantor2.service.engine.ai.core.message.AiMessage;
import io.terminus.trantor2.service.engine.ai.core.message.ChatMessageRole;
import io.terminus.trantor2.service.engine.ai.llm.LlmClientService;
import io.terminus.trantor2.service.engine.ai.platform.utils.AIUtil;
import io.terminus.trantor2.service.engine.exception.ServiceExecuteException;
import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class LlmExecution extends AbstractExecution {

    public LlmExecution(ServiceElement<?> currentNode) {
        super(currentNode);
    }

    @Override
    public void execute(ExecutionContext context) {
        if (AIContext.safeGetContext().isEmpty()) {
            AIContext.init();
        }
        AIContext.getContext().setVariableContext(context);

        // @formatter:off
        final String sessionId   = context.getSessionId();
        final String serviceKey  = context.getServiceKey();
        final String nodeName    = getCurrentNodeName();
        final String nodeKey     = getCurrentNodeKey();
        final String auditSource = String.format("trantor-programmable [%s] node: [%s](%s)", serviceKey, nodeName, nodeKey);

        AIProperties properties = getProps(AIProperties.class);
        // LLM 基础参数
        final String modelProvider  = properties.getModelPublisher();
        final String modelType      = properties.getModelType();
        final String modelName      = properties.getModelName();
        final Double temperature    = properties.getTemperature();
        final Integer maxTokens     = properties.getMaxTokens();
        final String responseFormat = properties.getResponseFormat();
        // 图片模型参数
        final String quality        = properties.getQuality();
        final String size           = properties.getSize();
        // 音频模型参数
        final String audioPathVariable = properties.getAudioPath();

        // 模型扩展能力参数
        final List<SkillTool> tools = properties.getSkillTools();
        final boolean visualUnderstanding = properties.getVisualUnderstanding();
        // 多模态视觉识别附件、语音转录附件
        final List<String> attachments = getAttachments(context, properties, visualUnderstanding);

        // 业务参数
        final boolean enableConversationHistory = properties.getConversationHistory();
        final boolean streamOutput = properties.getStream();

        //  基础校验
        if (StringUtils.isEmpty(modelType)) {
            throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_PARAM_NULL, new Object[]{"modelType"});
        }

        if (StringUtils.isEmpty(modelName)) {
            throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_PARAM_NULL, new Object[]{"modelName"});
        }

        // 如果含有工具则添加工具映射
        if (Objects.nonNull(tools) && !tools.isEmpty()) {
            AIContext.getContext().putToolsMapping(auditSource, tools);
        }

        String systemMessage = getMessageFromContext(context, properties, ChatMessageRole.SYSTEM);
        String userMessage = getMessageFromContext(context, properties, ChatMessageRole.USER);

        Class<?> sseMessageResponseType = String.class;
        if (context.getServiceEngine().getAiEngine().getAiProperties().isSseMessageBodyStructured()) {
            sseMessageResponseType = AiMessage.class;
        }

        // 多轮对话需要的参数
        Map<String, Object> payload = new HashMap<>();
        payload.put("userContent", userMessage);
        payload.put("attachments", attachments);
        ChatSession chatSession = context.getServiceEngine().getAiEngine().getChatSessionManager().create(sessionId, serviceKey, null, false);
        if (!enableConversationHistory) {
            chatSession.closeMemory();
        }
        chatSession.setPayload(payload);

        LlmClientService llmClientService = context.getServiceEngine().getAiEngine().getLlmClientService();
        Object output = null;

        LlmModelType currentModelType = EnumUtils.getEnum(LlmModelType.class, modelType.toUpperCase());
        switch (currentModelType) {
            case TEXT_GENERATION:
            case MULTIMODAL:
                ChatCompletionCreateParams chatCompletionParams = buildChatCompletionParams(context,
                                                                                            modelName,
                                                                                            maxTokens,
                                                                                            temperature,
                                                                                            systemMessage,
                                                                                            userMessage,
                                                                                            tools,
                                                                                            attachments,
                                                                                            enableConversationHistory,
                                                                                            visualUnderstanding);

                output = llmClientService.chatCompletionStream(modelProvider, modelName, chatCompletionParams, sseMessageResponseType, auditSource, chatSession, streamOutput);
                break;
            case IMAGE:
                ImageGenerateParams imageGenerateParams = llmClientService.buildImageGenerateParams(modelName, userMessage, responseFormat, quality, size);
                output = llmClientService.imagesGenerate(modelProvider, modelName, imageGenerateParams, sseMessageResponseType, auditSource);
                break;
            case AUDIO:
                TranscriptionCreateParams audioTranscriptionParams = buildAudioTranscriptionParams(context,
                                                                                                   properties,
                                                                                                   modelName,
                                                                                                   userMessage,
                                                                                                   responseFormat,
                                                                                                   audioPathVariable);
                output = llmClientService.audioTranscriptionStream(modelProvider, modelName, audioTranscriptionParams, sseMessageResponseType, auditSource);
                break;
            case EMBEDDING:
                break;
            default:
                throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_PARAM_INCORRECT, new Object[]{"modelType"});
        }

        if (CollectionUtils.isNotEmpty(properties.getOutput())) {
            Map<String, Object> result = convertResponseData(properties, output);
            setExecutionResult(context, result, properties.getOutputAssign());
            Contexts.getServiceTrace().trace("大模型返回结果: ", result);
        }
    }

    private ChatCompletionCreateParams buildChatCompletionParams(ExecutionContext context, String modelName, Integer maxTokens,
                                                                 Double temperature, String systemMessage, String userMessage,
                                                                 List<SkillTool> tools, List<String> attachments,
                                                                 boolean enableConversationHistory, boolean visualUnderstanding) {
        // @formatter:off
        ChatCompletionCreateParams.Builder builder = ChatCompletionCreateParams.builder()
                                                                               .model(modelName)
                                                                               .maxCompletionTokens(maxTokens)
                                                                               .temperature(temperature)
                                                                               .streamOptions(ChatCompletionStreamOptions.builder()
                                                                                                                         .includeUsage(true)
                                                                                                                         .build());
        // @formatter:on
        buildChatMessage(context, builder, systemMessage, userMessage, attachments, enableConversationHistory, visualUnderstanding);

        if (Objects.nonNull(tools) && !tools.isEmpty()) {
            LlmClientService llmClientService = context.getServiceEngine().getAiEngine().getLlmClientService();
            List<ChatCompletionTool> chatCompletionTools = llmClientService.buildChatCompletionTools(tools);
            builder.tools(chatCompletionTools);
        }
        return builder.build();
    }

    private TranscriptionCreateParams buildAudioTranscriptionParams(ExecutionContext context, AIProperties properties,
                                                                    String modelName, String userMessage,
                                                                    String responseFormat, String audioPathVariable) {
        LlmClientService llmClientService = context.getServiceEngine().getAiEngine().getLlmClientService();
        String audioLocalPath = convertAudioPath(context, properties, audioPathVariable);
        return llmClientService.buildAudioTranscriptionParams(modelName, userMessage, responseFormat, audioLocalPath);
    }

    public String convertAudioPath(ExecutionContext context, AIProperties properties, String audioPathVar) {
        // 音频文件OSS地址可能是由参数变量带进来的需要对变量进行转换
        List<StringEntry> audioPathPlaceholderMapping = properties.getAudioPathPlaceholderMapping();
        String audioPath;
        if (Objects.nonNull(audioPathPlaceholderMapping) && !audioPathPlaceholderMapping.isEmpty()) {
            audioPath = replaceHolder(audioPathVar, audioPathPlaceholderMapping, context);
        } else {
            audioPath = audioPathVar;
        }

        return AIUtil.getFileDumpToLocalPathFromOSS(audioPath, true);
    }

    /**
     * 将AI调用结果进行数据格式的转化
     *
     * @param properties
     * @param response
     * @return
     */
    private Map<String, Object> convertResponseData(AIProperties properties, Object response) {
        Map<String, Object> outputMapping;
        List<Field> output = properties.getOutput();
        if (output.size() > 1) {
            // 如果输出配置了多个字段则AI返回的结果必须要求是JSON对象
            if (JSONValidator.from((String) response).validate()) {
                outputMapping = new HashMap<>(8);
                JSONObject result = JsonUtil.fromJson((String) response, JSONObject.class);
                if (null == result || result.isEmpty()) {
                    return Collections.emptyMap();
                }
                for (Field field : output) {
                    String fieldName = field.getFieldKey();
                    if (result.containsKey(fieldName)) {
                        Object fieldValue;
                        fieldValue = parseFieldValue(field, result, fieldName, true);
                        outputMapping.putIfAbsent(fieldName, fieldValue);
                    }
                }
            } else {
                throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_AI_DATA_CONVERT_ERROR);
            }
        } else {
            Field field = output.get(0);
            String fieldName = field.getFieldKey();
            outputMapping = MapUtil.of(fieldName, response);
        }
        return outputMapping;
    }

    /**
     * 解析并获取字段值
     *
     * @param field
     * @param source
     * @param fieldName
     * @param innerScope
     * @return
     */
    private static Object parseFieldValue(Field field, Object source, String fieldName, boolean innerScope) {
        Object fieldValue;
        switch (field.getFieldType()) {
            case Boolean:
                if (innerScope) {
                    fieldValue = ((JSONObject) source).getBoolean(fieldName);
                } else {
                    fieldValue = Boolean.parseBoolean((String) source);
                }
                break;
            case Array:
                if (innerScope) {
                    fieldValue = ((JSONObject) source).getJSONArray(fieldName);
                } else {
                    if (JSONValidator.from((String) source).validate()) {
                        fieldValue = JsonUtil.fromJson((String) source, JSONArray.class);
                    } else {
                        throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_AI_DATA_CONVERT_ERROR);
                    }
                }
                break;
            case Object:
            case Paging:
            case Pageable:
            case Model:
                if (innerScope) {
                    fieldValue = ((JSONObject) source).getJSONObject(fieldName);
                } else {
                    if (JSONValidator.from((String) source).validate()) {
                        fieldValue = JsonUtil.fromJson((String) source, JSONObject.class);
                    } else {
                        throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_AI_DATA_CONVERT_ERROR);
                    }
                }
                break;
            default:
                if (innerScope) {
                    fieldValue = ((JSONObject) source).getString(fieldName);
                } else {
                    fieldValue = String.valueOf(source);
                }
                break;
        }
        return fieldValue;
    }

    /**
     * 获取对话Message
     *
     * @param context
     * @param builder
     * @param systemMessage
     * @param userMessage
     * @param attachments
     * @param enableConversationHistory
     * @param visualUnderstanding
     */
    private void buildChatMessage(ExecutionContext context, ChatCompletionCreateParams.Builder builder, String systemMessage,
                                  String userMessage, List<String> attachments, boolean enableConversationHistory,
                                  boolean visualUnderstanding) {
        List<ChatCompletionMessageParam> messages = new ArrayList<>();
        if (enableConversationHistory) {
            messages = getChatHistory(context);
            if (Objects.isNull(messages) || messages.isEmpty()) {
                log.info("current sessionId: [{}] chat history not found", context.getSessionId());
            }
        }

        // 开启视觉识别并附件不为空时才会构建视觉识别的Message上下文
        if (visualUnderstanding && CollectionUtils.isNotEmpty(attachments)) {
            // 此时需要默认将系统提示词角色类型换成用户提示词角色类型，避免个别模型不支持系统提示词的情况
            if (StringUtils.isNotBlank(systemMessage)) {
                builder.addUserMessage(systemMessage);
            }

            List<ChatCompletionContentPart> contentParts = new ArrayList<>();
            for (String attachmentUrl : attachments) {
                // @formatter:off
                contentParts.add(ChatCompletionContentPart.ofImageUrl(ChatCompletionContentPartImage.builder()
                                                                                                    .imageUrl(ChatCompletionContentPartImage.ImageUrl.builder()
                                                                                                                                                     .url(attachmentUrl)
                                                                                                                                                     .build())
                                                                                                    .build()));
                // @formatter:on
            }

            contentParts.add(ChatCompletionContentPart.ofText(ChatCompletionContentPartText.builder().text(userMessage).build()));

            // 历史记录
            if (Objects.nonNull(messages) && !messages.isEmpty()) {
                builder.messages(messages);
            }
            // 当前提示词（携带附件）
            builder.addUserMessageOfArrayOfContentParts(contentParts);
        } else {
            // 系统提示词
            messages.add(0, ChatCompletionMessageParam.ofSystem(ChatCompletionSystemMessageParam.builder().content(systemMessage).build()));
            // 历史记录
            builder.messages(messages);
            // 当前提示词
            builder.addUserMessage(userMessage);
        }
    }

    private List<ChatCompletionMessageParam> getChatHistory(ExecutionContext context) {
        String sessionId = context.getSessionId();
        return context.getServiceEngine()
                .getAiEngine()
                .getMemoryManager()
                .getHistoryMessages(sessionId, 10, null, AIUtil::convertHistoryMessage)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取附件
     *
     * @param context
     * @param properties
     * @param isVisualUnderstanding
     * @param <T>
     * @return
     */
    public <T> List<T> getAttachments(ExecutionContext context, AIProperties properties, boolean isVisualUnderstanding) {
        Object value = ValueFactory.getValue(null, properties.getAttachments(), context);
        List<T> attachments = new ArrayList<>();
        if (value instanceof List) {
            if (((List<?>) value).get(0) instanceof String) {
                attachments.addAll((Collection<? extends T>) value);
            } else if (((List<?>) value).get(0) instanceof Map) {
                // 当前可能是前端传递的附件复杂入参
                if (isVisualUnderstanding) {
                    // @formatter:off
                    List<?> urls = ((List<?>) value).stream()
                                                    .map(item -> ((Map<?, ?>) item).getOrDefault("url", null))
                                                    .filter(Objects::nonNull)
                                                    .collect(Collectors.toList());
                    // @formatter:on
                    attachments.addAll((Collection<? extends T>) urls);
                } else {
                    return (List<T>) value;
                }
            }
        }
        return attachments;
    }

    /**
     * 获取宏变量替换之后的内容
     *
     * @param context
     * @param properties
     * @param role
     * @return
     */
    private String getMessageFromContext(ExecutionContext context, AIProperties properties, ChatMessageRole role) {
        String message = null;
        List<StringEntry> placeholderMapping = null;
        switch (role) {
            case USER:
                message = StringUtils.defaultIfBlank(properties.getUserMessageEn(), properties.getUserMessage());
                placeholderMapping = properties.getUserMessagePlaceholderMapping();
                break;
            case SYSTEM:
                message = StringUtils.defaultIfBlank(properties.getSystemMessageEn(), properties.getSystemMessage());
                placeholderMapping = properties.getSysMessagePlaceholderMapping();
                break;
            default:
                break;
        }
        if (StringUtils.isBlank(message) && role.equals(ChatMessageRole.USER)) {
            log.warn("ai chat user message is empty");
            throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_PARAM_NULL, "用户Message为空");
        }
        if (StringUtils.isNotBlank(message)) {
            message = replaceHolder(message, placeholderMapping, context);
            message = replaceTemplate(message, context);
        }
        return message;
    }

    private String replaceTemplate(String message, ExecutionContext context) {
        if (message.contains("{{") && message.contains("}}")) {
            return Placeholder.PLACE_DOUBLE_BRACES_HOLDER.replaceHolder(message, (key) -> getTemplateContent(key, context), true);
        }
        return message;
    }

    private String getTemplateContent(String templateCode, ExecutionContext context) {
        // @formatter:off
        Map<String, Object> model = context.getServiceEngine()
                                           .getSystemServiceWrapper()
                                           .findOne(context.getTeamId(),
                                                    AIConst.TEMPLATE_MODEL,
                                                    MapUtil.of(AIConst.TEMPLATE_CODE_FIELD, templateCode));
        // @formatter:on
        if (model != null) {
            return (String) model.get(AIConst.TEMPLATE_CONTENT_FIELD);
        }
        return null;
    }

    private String replaceHolder(String message, List<StringEntry> placeholderMapping, ExecutionContext context) {
        if (message.contains("${") && message.contains("}") && placeholderMapping != null) {
            return Placeholder.PLACE_DOLLAR_BRACES_HOLDER.replaceHolder(message, ValueFactory.getValueForStringEntry(placeholderMapping, context));
        }
        return message;
    }
}
