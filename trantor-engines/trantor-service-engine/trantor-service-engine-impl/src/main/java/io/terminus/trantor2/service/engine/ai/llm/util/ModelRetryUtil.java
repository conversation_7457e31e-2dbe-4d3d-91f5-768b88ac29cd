package io.terminus.trantor2.service.engine.ai.llm.util;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 模型重试工具类
 * 支持多模型降级重试策略
 */
@Slf4j
public class ModelRetryUtil {
    private static final String DEFAULT_DESCRIPTION = "默认模型";
    private static final Integer DEFAULT_MAX_COMPLETION_TOKENS = 8192;

    /**
     * 模型配置
     */
    @Getter
    @AllArgsConstructor
    public static class ModelConfig implements Serializable {
        private static final long serialVersionUID = -4451962647518537355L;

        private final String provider;
        private final String modelName;
        private final String description;
        private final Integer maxCompletionTokens;

        @Override
        public String toString() {
            return String.format("%s - %s (%s, maxTokens: %d)", provider,
                                                                modelName,
                                                                StringUtils.hasText(description) ? description : DEFAULT_DESCRIPTION,
                                                                maxCompletionTokens != null ? maxCompletionTokens : DEFAULT_MAX_COMPLETION_TOKENS);
        }
    }

    /**
     * 模型调用函数接口
     */
    @FunctionalInterface
    public interface ModelInvoker<T> {
        T invoke(ModelConfig config) throws Exception;
    }

    /**
     * 执行带重试的模型调用
     */
    public static <T> T executeWithRetry(List<ModelConfig> modelConfigs, ModelInvoker<T> invoker) {
        if (modelConfigs == null || modelConfigs.isEmpty()) {
            throw new IllegalArgumentException("模型配置列表不能为空");
        }

        Exception lastException = null;

        for (int i = 0; i < modelConfigs.size(); i++) {
            ModelConfig config = modelConfigs.get(i);

            try {
                log.info("尝试使用模型: {}", config);
                T result = invoker.invoke(config);
                log.info("模型 {} 调用成功", config);
                return result;

            } catch (Exception e) {
                lastException = e;
                log.warn("模型 {} 调用失败: {}", config, e.getMessage());

                if (i < modelConfigs.size() - 1) {
                    log.info("尝试降级到下一个模型...");
                } else {
                    log.error("所有模型都调用失败");
                }
            }
        }

        throw new RuntimeException("所有模型调用都失败", lastException);
    }

    /**
     * 便捷方法：使用数组
     */
    public static <T> T executeWithRetry(ModelConfig[] modelConfigs, ModelInvoker<T> invoker) {
        return executeWithRetry(Arrays.asList(modelConfigs), invoker);
    }

    /**
     * 便捷方法：创建模型配置
     */
    public static ModelConfig createConfig(String provider, String modelName, Integer maxCompletionTokens, String description) {
        return new ModelConfig(provider, modelName, description, maxCompletionTokens);
    }
}
