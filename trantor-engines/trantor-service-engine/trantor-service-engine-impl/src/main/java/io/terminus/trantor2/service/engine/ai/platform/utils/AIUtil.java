package io.terminus.trantor2.service.engine.ai.platform.utils;

import cn.hutool.http.HttpUtil;
import com.google.common.collect.Maps;
import com.openai.models.chat.completions.ChatCompletion;
import com.openai.models.chat.completions.ChatCompletionAssistantMessageParam;
import com.openai.models.chat.completions.ChatCompletionChunk;
import com.openai.models.chat.completions.ChatCompletionMessageParam;
import com.openai.models.chat.completions.ChatCompletionSystemMessageParam;
import com.openai.models.chat.completions.ChatCompletionUserMessageParam;
import dev.ai4j.openai4j.chat.AssistantMessage;
import dev.ai4j.openai4j.chat.ChatCompletionChoice;
import dev.ai4j.openai4j.chat.ChatCompletionResponse;
import dev.ai4j.openai4j.chat.Delta;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.ApplicationContextUtil;
import io.terminus.trantor2.service.common.consts.AIConst;
import io.terminus.trantor2.service.common.utils.TrantorSseEmitter;
import io.terminus.trantor2.service.dsl.AINode;
import io.terminus.trantor2.service.engine.ai.configuration.AiProperties;
import io.terminus.trantor2.service.engine.ai.consts.SseMessageBodyType;
import io.terminus.trantor2.service.engine.ai.core.chat.session.ChatSession;
import io.terminus.trantor2.service.engine.ai.core.message.AIProgressStatus;
import io.terminus.trantor2.service.engine.ai.core.message.AiMessage;
import io.terminus.trantor2.service.engine.ai.core.message.AttachmentsContent;
import io.terminus.trantor2.service.engine.ai.core.message.ChatMessageRole;
import io.terminus.trantor2.service.engine.ai.core.message.Content;
import io.terminus.trantor2.service.engine.ai.core.message.ErrorContent;
import io.terminus.trantor2.service.engine.ai.core.message.Message;
import io.terminus.trantor2.service.engine.ai.core.message.MessageBuilder;
import io.terminus.trantor2.service.engine.ai.core.message.SystemMessage;
import io.terminus.trantor2.service.engine.ai.core.message.TextContent;
import io.terminus.trantor2.service.engine.ai.core.message.ToolCallContent;
import io.terminus.trantor2.service.engine.ai.core.message.ToolOutputContent;
import io.terminus.trantor2.service.engine.ai.core.message.UserMessage;
import io.terminus.trantor2.service.engine.ai.platform.AIProgress;
import io.terminus.trantor2.service.engine.ai.platform.impl.TAIRestHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.springframework.util.Base64Utils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class AIUtil {
    // @formatter:off
    // 调用AIProxy的AI模型默认配置
    public static final String DEFAULT_MODEL_PROVIDER      = "openai";
    public static final String DEFAULT_MODEL_NAME          = "gpt-4o-mini";
    public static final Double DEFAULT_TEMPERATURE         = 0.7D;
    public static final Integer DEFAULT_MAX_TOKENS         = 2048;

    // SSE发起连接请求超时时间
    public static final Integer DEFAULT_SSE_TIMEOUT_HOURS  = 3;

    // 大模型客户端超时时间
    public static final Duration DEFAULT_TIMEOUT           = Duration.ofMinutes(10);

    // 最大重试次数
    public static final Integer DEFAULT_MAX_RETRIES        = 3;

    // SSE终止标识符常量
    public static final String SSE_MESSAGE_DONE            = "[DONE]";
    // @formatter:on

    public static Map<String, String> buildHttpAuditHeader(User user, String modelProvider, String source) {
        StringBuilder auditMessage = new StringBuilder();
        Long userId = AIConst.DEFAULT_USER_ID_IF_NOT_EXIST;
        Long jobNumber = AIConst.DEFAULT_USER_ID_IF_NOT_EXIST;
        String username = AIConst.DEFAULT_USERNAME_IF_NOT_EXIST;
        String email = null;
        String phone = null;
        if (user != null) {
            if (Objects.nonNull(user.getId())) {
                userId = user.getId();
                jobNumber = user.getId();
            }
            if (StringUtils.isNotBlank(user.getUsername())) {
                username = user.getUsername();
            }
            if (StringUtils.isNotBlank(user.getEmail())) {
                email = user.getEmail();
            }
            if (StringUtils.isNotBlank(user.getMobile())) {
                phone = user.getMobile();
            }
        } else {
            log.warn("current user is null");
            username = "anonymous";
            userId = -1L;
        }
        auditMessage.append(String.format("user %s(%s) will invoke ai", username, userId));
        Map<String, String> headers = Maps.newHashMap();
        headers.put(AIConst.AI_PROXY_HEADER_AUDIT_USER_ID, Base64Utils.encodeToString(String.valueOf(userId).getBytes(StandardCharsets.UTF_8)));
        headers.put(AIConst.AI_PROXY_HEADER_AUDIT_JOB_NUMBER, Base64Utils.encodeToString(String.valueOf(jobNumber).getBytes(StandardCharsets.UTF_8)));
        headers.put(AIConst.AI_PROXY_HEADER_AUDIT_USERNAME, Base64Utils.encodeToString(username.getBytes(StandardCharsets.UTF_8)));
        if (StringUtils.isNotBlank(email)) {
            headers.put(AIConst.AI_PROXY_HEADER_AUDIT_EMAIL, Base64Utils.encodeToString(email.getBytes(StandardCharsets.UTF_8)));
        }
        if (StringUtils.isNotBlank(phone)) {
            headers.put(AIConst.AI_PROXY_HEADER_AUDIT_PHONE, Base64Utils.encodeToString(phone.getBytes(StandardCharsets.UTF_8)));
        }
        if (StringUtils.isNotBlank(modelProvider)) {
            headers.put(AIConst.AI_PROXY_HEADER_AUDIT_MODEL_PUBLISHER, modelProvider);
            auditMessage.append(String.format(", use modelProvider: [%s]", modelProvider));
        }
        if (StringUtils.isNotBlank(source)) {
            headers.put(AIConst.AI_PROXY_HEADER_AUDIT_SOURCE, Base64Utils.encodeToString(source.getBytes(StandardCharsets.UTF_8)));
            auditMessage.append(String.format(", from source: [%s]", source));
        }
        if (StringUtils.isNotBlank(TrantorContext.getTraceId())) {
            headers.put(AIConst.AI_PROXY_HEADER_TRACE_ID, TrantorContext.getTraceId());
            log.info("trace id: {}", TrantorContext.getTraceId());
        }
        log.info(auditMessage.toString());
        return headers;
    }

    public static String getFileDumpToLocalPathFromOSS(String fileOriginalUrl, boolean audioFile) {
        String fileUrl = fileOriginalUrl;
        String storagePath;
        AiProperties aiProperties = (AiProperties) ApplicationContextUtil.getApplicationContext().getBean("aiProperties");
        if (StringUtils.isNotBlank(aiProperties.getStoragePath())) {
            storagePath = aiProperties.getStoragePath();
        } else {
            storagePath = AIConst.AI_STORAGE_PATH;
        }
        if (fileUrl.lastIndexOf("?Expires=") >= 0) {
            fileUrl = fileUrl.substring(0, fileUrl.lastIndexOf("?Expires="));
        }
        String[] urlPaths = fileUrl.split("/");
        String fileName = urlPaths[urlPaths.length - 1];
        String fileParent = urlPaths[urlPaths.length - 2];
        String fileParentPath = String.format("%s/audio/%s", storagePath, fileParent);
        String fileDestPath = String.format("%s/%s", fileParentPath, fileName);
        String convertedFileDestPath = fileDestPath;
        if (audioFile) {
            String convertedFileName = convertMp3File(fileName);
            convertedFileDestPath = String.format("%s/%s", fileParentPath, convertedFileName);
        }
        try {
            File dir = new File(fileParentPath);
            if (!dir.exists()) {
                // 不存在目录则先创建目录
                boolean created = dir.mkdirs();
                if (!created) {
                    log.error("dir create error");
                }
            }
            File file = new File(fileDestPath);
            if (!file.exists()) {
                // 目录文件不存在则先进行网络下载
                HttpUtil.downloadFile(fileOriginalUrl, fileDestPath);
                if (new File(fileDestPath).exists()) {
                    // 如果是音频文件则还需要调用T-AI接口将格式统一转换为mp3
                    if (audioFile) {
                        TAIRestHandler taiRestHandler = ApplicationContextUtil.getApplicationContext().getBean(TAIRestHandler.class);
                        taiRestHandler.audioConvert(fileDestPath, convertedFileDestPath);
                    }
                } else {
                    log.error("file file download error from oss: {}", fileOriginalUrl);
                }
            }
        } catch (Exception e) {
            log.error("download file error, dest path maybe no permission", e);
        }
        return fileDestPath;
    }

    private static String convertMp3File(String fileName) {
        String audioSuffix = "mp3";
        int index = fileName.lastIndexOf(".");
        if (index > 0) {
            return String.format("%s.%s", fileName.substring(0, index), audioSuffix);
        } else {
            return String.format("%s.%s", fileName, audioSuffix);
        }
    }

    public static void logTrace(Logger log, StringBuilder response, int promptTokens, int completionTokens,
                                String modelName, long startTime) {
        logTrace(log, response, promptTokens, completionTokens, promptTokens + completionTokens, modelName, startTime);
    }

    public static void logTrace(Logger log, StringBuilder response, int promptTokens, int completionTokens,
                                int totalTokens, String modelName, long startTime) {
        log.info("ai execute response \n{}", response.toString());
        log.info("ai execute cost: {}s", (System.currentTimeMillis() - startTime) / 1000.0);
        log.info("ai prompt tokens: {}", promptTokens);
        log.info("ai completion tokens: {}", completionTokens);
        log.info("ai total tokens: {}", totalTokens);
        log.info("ai use model: {}", modelName);
    }

    public static void publishSseEmitterInProgress(AIProgress handler, String serviceKey, String nodeKey, String nodeName) {
        String eventName;
        String progressMessage;
        String action = handler.getNode() == AINode.class ? "执行" : "搜索";
        switch (handler.getStatus()) {
            case DOING:
                eventName = String.format("%s#doing", serviceKey);
                progressMessage = String.format("%s%s中", nodeName, action);
                break;
            case DONE:
                eventName = String.format("%s#done", serviceKey);
                progressMessage = String.format("%s%s完毕", nodeName, action);
                break;
            case INTERRUPTION:
                eventName = String.format("%s#interruption", serviceKey);
                progressMessage = String.format("%s%s发生异常，已中断", nodeName, action);
                break;
            default:
                eventName = String.format("%s#unknown", serviceKey);
                progressMessage = "未知执行状态";
                break;
        }
        try {
            log.info("service node {}({}) {}", nodeName, String.format("%s$%s", serviceKey, nodeKey), progressMessage);
            handler.getSseEmitter().send(TrantorSseEmitter.event().name(eventName).data(progressMessage));
        } catch (Exception e) {
            log.error("send message error in progress", e);
        }
    }

    /**
     * 接收并给SSE发送大模型的正文部分
     *
     * @param chunk        stream流chunk片段
     * @param sseEmitter   SSE
     * @param responseType 响应类型
     * @param output       完整的输出内容
     * @Deprecated 移除SDK Adapter后，删除该方法
     */
    @Deprecated
    public static <R, T> void sendSseMessageWithResponseType(R chunk, ChatSession chatSession, SseEmitter sseEmitter,
                                                             Class<T> responseType, StringBuilder output) {
        sendSseMessageWithResponseType(chunk, chatSession, sseEmitter, responseType, output, true);
    }

    public static <R, T> void sendSseMessageWithResponseType(R chunk, ChatSession chatSession, SseEmitter sseEmitter,
                                                             Class<T> responseType, StringBuilder output, boolean sseOutput) {
        T sseMessage;
        if (chunk instanceof String content) {
            output.append(content);
            sseMessage = buildSseMessage(assertMessageType(responseType), chatSession, content);
        } else {
            sseMessage = buildMessageByChunk(chunk, responseType, true, chatSession, output);
        }

        if (Objects.nonNull(sseMessage) && sseOutput) {
            try {
                sseEmitter.send(sseMessage);
            } catch (Exception e) {
                log.error("send sse message error: {}", e.getMessage(), e);
            }
        }
    }

    public static <R, T> T buildMessageByChunk(R chunk, Class<T> responseType, boolean streaming, ChatSession chatSession, StringBuilder output) {
        SseMessageBodyType type = assertMessageType(responseType);
        String tokenContent = parseChatCompletionResult(chunk, streaming, output);
        return buildSseMessage(type, chatSession, tokenContent);
    }

    /**
     * 根据传入的类型解析Chunk内容
     *
     * @param chunk     stream流chunk片段
     * @param streaming llm开启streaming
     * @param output    解析chunk内容输出
     * @return 解析后的内容
     */
    public static <R> String parseChatCompletionResult(R chunk, boolean streaming, StringBuilder output) {
        String content = "";
        if (chunk instanceof ChatCompletionResponse) {
            List<ChatCompletionChoice> choices = ((ChatCompletionResponse) chunk).choices();
            if (CollectionUtils.isNotEmpty(choices)) {
                if (streaming) {
                    Delta delta = choices.get(0).delta();
                    content = delta.content();
                } else {
                    AssistantMessage message = choices.get(0).message();
                    content = message.content();
                }
                output.append(content);
            }
        } else if (chunk instanceof ChatCompletionChunk) {
            List<ChatCompletionChunk.Choice> choices = ((ChatCompletionChunk) chunk).choices();
            if (CollectionUtils.isNotEmpty(choices)) {
                for (ChatCompletionChunk.Choice choice : choices) {
                    content = choice.delta().content().orElse("");
                    output.append(content);
                }
            }
        } else if (chunk instanceof ChatCompletion) {
            List<ChatCompletion.Choice> choices = ((ChatCompletion) chunk).choices();
            if (CollectionUtils.isNotEmpty(choices)) {
                for (ChatCompletion.Choice choice : choices) {
                    content = choice.message().content().orElse("");
                    output.append(content);
                }
            }
        }

        return content;
    }

    public static <T> T buildSseMessage(SseMessageBodyType type, ChatSession chatSession, String content) {
        switch (type) {
            case STRING:
                if (Objects.nonNull(chatSession)) {
                    TextContent textContent = TextContent.ofText(content != null ? content : "", AIProgressStatus.DOING);
                    AiMessage aiMessage = buildAiMessage(textContent);
                    chatSession.onMessage(aiMessage);
                }
                return (T) content;
            case AI_MESSAGE:
                TextContent textContent = TextContent.ofText(content != null ? content : "", AIProgressStatus.DOING);
                AiMessage aiMessage = buildAiMessage(textContent);
                if (Objects.nonNull(chatSession)) {
                    chatSession.onMessage(aiMessage);
                }
                return (T) aiMessage;
            default:
                log.error("current not support other sse message body type: {}, only String or AiMessage", type);
                return null;
        }
    }

    /**
     * 根据类型获取SSE消息实体类型枚举
     *
     * @param clazz 类型
     * @return SSE消息实体类型枚举
     */
    private static SseMessageBodyType assertMessageType(Class<?> clazz) {
        if (String.class.isAssignableFrom(clazz)) {
            return SseMessageBodyType.STRING;
        } else if (AiMessage.class.isAssignableFrom(clazz)) {
            return SseMessageBodyType.AI_MESSAGE;
        }
        throw new IllegalArgumentException("Unsupported response type: " + clazz.getName());
    }

    /**
     * 处理异常并发送错误消息到SSE
     *
     * @param sseEmitter SSE发射器
     * @param throwable  异常
     */
    public static void sendSseMessageWithError(SseEmitter sseEmitter, Throwable throwable) {
        try {
            AiMessage aiMessage = buildErrorAiMessage(throwable);
            sseEmitter.send(SseEmitter.event().data(aiMessage));
            AiProperties aiProperties = (AiProperties) ApplicationContextUtil.getApplicationContext().getBean("aiProperties");
            if (aiProperties.isSseMessageBodyStructured()) {
                sseEmitter.send(SseEmitter.event().data(SSE_MESSAGE_DONE));
            }
            sseEmitter.complete();
        } catch (IOException ex) {
            log.error("Failed to send error message via SSE: {}", ex.getMessage(), ex);
            try {
                sseEmitter.completeWithError(ex);
            } catch (Exception e) {
                log.error("Failed to complete SSE with error: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 构建AI空消息对象
     *
     * @return AI消息对象
     */
    public static AiMessage buildEmptyAiMessage() {
        TextContent content = TextContent.ofText("", AIProgressStatus.DOING);
        return buildAiMessage(content);
    }

    /**
     * 构建AI报错消息对象
     *
     * @param throwable 异常
     * @return AI消息对象
     */
    public static AiMessage buildErrorAiMessage(Throwable throwable) {
        ErrorContent content = new ErrorContent();
        content.setDetails(throwable.getMessage());
        return buildAiMessage(content);
    }

    /**
     * 构建AI消息对象
     *
     * @param content 消息内容
     * @return AI消息对象
     */
    @NotNull
    public static AiMessage buildAiMessage(Content content) {
        return MessageBuilder.newBuilder()
                .autoMessageId()
                .createdAt(Calendar.getInstance().getTimeInMillis())
                .build(content);
    }

    public static ChatCompletionMessageParam buildMessage(ChatMessageRole role, String message) {
        return buildMessage(role, message, null);
    }

    public static ChatCompletionMessageParam buildMessage(ChatMessageRole role, String message, List<String> attachments) {
        switch (role) {
            case SYSTEM:
                ChatCompletionSystemMessageParam systemMessage = ChatCompletionSystemMessageParam.builder().content(message).build();
                return ChatCompletionMessageParam.ofSystem(systemMessage);
            case USER:
                if (Objects.nonNull(attachments) && !attachments.isEmpty()) {
                    // TODO 待考虑
                } else {
                    ChatCompletionUserMessageParam userMessage = ChatCompletionUserMessageParam.builder().content(message).build();
                    return ChatCompletionMessageParam.ofUser(userMessage);
                }
            case ASSISTANT:
                ChatCompletionAssistantMessageParam assistantMessage = ChatCompletionAssistantMessageParam.builder().content(message).build();
                return ChatCompletionMessageParam.ofAssistant(assistantMessage);
            default:
                return null;
        }
    }

    public static ChatCompletionMessageParam convertHistoryMessage(Message message) {
        if (message instanceof SystemMessage) {
            return buildMessage(ChatMessageRole.SYSTEM, ((SystemMessage) message).getContent());
        } else if (message instanceof UserMessage) {
            Content content = ((UserMessage) message).getContent().get(0);
            if (content instanceof TextContent) {
                return buildMessage(ChatMessageRole.USER, ((TextContent) content).getText());
            } else if (content instanceof AttachmentsContent) {
                // TODO 附件待处理
            }
        } else if (message instanceof AiMessage) {
            Content content = ((AiMessage) message).getContent();
            if (content instanceof TextContent) {
                return buildMessage(ChatMessageRole.ASSISTANT, ((TextContent) content).getText());
            } else if (content instanceof ToolCallContent) {
            } else if (content instanceof ToolOutputContent) {
            }
        }
        return null;
    }
}
