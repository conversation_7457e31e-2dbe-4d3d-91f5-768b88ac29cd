package io.terminus.trantor2.service.engine.ai.llm;

import com.openai.models.chat.completions.ChatCompletion;
import com.openai.models.chat.completions.ChatCompletionChunk;
import io.terminus.trantor2.service.engine.ai.configuration.AiProperties;
import io.terminus.trantor2.service.engine.ai.core.chat.session.ChatSession;
import io.terminus.trantor2.service.engine.ai.core.message.AiMessage;
import io.terminus.trantor2.service.engine.ai.core.message.TokenContent;
import io.terminus.trantor2.service.engine.ai.core.message.Usage;
import io.terminus.trantor2.service.engine.ai.llm.tool.LlmToolCallHandler;
import io.terminus.trantor2.service.engine.ai.llm.tool.LlmToolCallMetadata;
import io.terminus.trantor2.service.engine.ai.platform.utils.AIUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
@AllArgsConstructor
public class LlmResponseHandler {
    private final LlmToolCallHandler llmToolCallHandler;
    private final AiProperties aiProperties;

    public <R> void handleResponse(ChatSession chatSession, R chunk, StringBuilder finalOutput, List<LlmToolCallMetadata> toolCallsMetadata) {
        String content;
        if (chunk instanceof ChatCompletionChunk) {
            List<ChatCompletionChunk.Choice> choices = ((ChatCompletionChunk) chunk).choices();
            if (CollectionUtils.isNotEmpty(choices)) {
                for (ChatCompletionChunk.Choice choice : choices) {
                    if (choice.delta().content().isPresent()) {
                        content = choice.delta().content().orElse("");
                        finalOutput.append(content);
                    }

                    if (choice.delta().toolCalls().isPresent()) {
                        handleToolCallsChoice(choice, toolCallsMetadata);
                    }

                    if (choice.finishReason().isPresent()) {
                        if (choice.finishReason().get().equals(ChatCompletionChunk.Choice.FinishReason.TOOL_CALLS)) {
                            llmToolCallHandler.handleToolArguments(toolCallsMetadata);
                        }
                    }
                }
            }
        } else if (chunk instanceof ChatCompletion) {
            List<ChatCompletion.Choice> choices = ((ChatCompletion) chunk).choices();
            if (CollectionUtils.isNotEmpty(choices)) {
                for (ChatCompletion.Choice choice : choices) {
                    if (choice.message().content().isPresent()) {
                        content = choice.message().content().orElse("");
                        finalOutput.append(content);
                    }

                    if (choice.message().toolCalls().isPresent()) {
                    }

                    if (choice.finishReason().equals(ChatCompletion.Choice.FinishReason.TOOL_CALLS)) {
                        llmToolCallHandler.handleToolArguments(toolCallsMetadata);
                    }
                }
            }
        }
    }

    public void handleToolCallsChoice(ChatCompletionChunk.Choice choice, List<LlmToolCallMetadata> toolCallsMetadata) {
        LlmToolCallMetadata llmToolCallMetadata;
        List<ChatCompletionChunk.Choice.Delta.ToolCall> toolCalls = choice.delta().toolCalls().get();
        for (ChatCompletionChunk.Choice.Delta.ToolCall toolCall : toolCalls) {
            if (toolCall.index() >= toolCallsMetadata.size()) {
                llmToolCallMetadata = new LlmToolCallMetadata();
                toolCallsMetadata.add(llmToolCallMetadata);
            } else {
                llmToolCallMetadata = toolCallsMetadata.get((int) toolCall.index());
            }

            if (toolCall.id().isPresent()) {
                String callId = toolCall.id().get();
                llmToolCallMetadata.setCallId(callId);
            }
            if (toolCall.function().isPresent()) {
                ChatCompletionChunk.Choice.Delta.ToolCall.Function function = toolCall.function().get();
                if (function.name().isPresent()) {
                    llmToolCallMetadata.setTooName(function.name().get());
                }
                if (function.arguments().isPresent() && StringUtils.isNotEmpty(function.arguments().get())) {
                    String arguments = llmToolCallMetadata.getArguments();
                    if (StringUtils.isNotEmpty(arguments)) {
                        arguments += function.arguments().get();
                    } else {
                        arguments = StringUtils.trimToNull(function.arguments().get());
                    }
                    llmToolCallMetadata.setArguments(arguments);
                }
            }
        }
    }

    public <T> void handleChunk(ChatSession chatSession, ChatCompletionChunk chunk, SseEmitter sseEmitter,
                                Class<T> responseType, StringBuilder finalOutput, boolean streamOutput) {
        AIUtil.sendSseMessageWithResponseType(chunk, chatSession, sseEmitter, responseType, finalOutput, streamOutput);

        chunk.usage().ifPresent(usage -> {
            try {
                if (aiProperties.isSseMessageBodyStructured()) {
                    // @formatter:off
                    Usage tokenUsage = Usage.builder()
                                            .inputTokens((int) usage.promptTokens())
                                            .outputTokens((int) usage.completionTokens())
                                            .totalTokens((int) usage.totalTokens())
                                            .build();
                    // @formatter:on
                    TokenContent content = new TokenContent();
                    content.setUsage(tokenUsage);
                    AiMessage tokenMessage = AIUtil.buildAiMessage(content);
                    if (Objects.nonNull(chatSession)) {
                        chatSession.onMessage(tokenMessage);
                    }
                    sseEmitter.send(SseEmitter.event().data(tokenMessage));
                }
            } catch (IOException e) {
                log.error("Error sending token usage on complete: {}", e.getMessage(), e);
                AIUtil.sendSseMessageWithError(sseEmitter, e);
            }
        });
    }
}
