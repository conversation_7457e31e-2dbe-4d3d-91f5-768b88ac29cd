package io.terminus.trantor2.service.engine.ai.llm.tool.execution;

import com.openai.core.JsonValue;
import com.openai.models.FunctionDefinition;
import com.openai.models.FunctionParameters;
import com.openai.models.chat.completions.ChatCompletionFunctionTool;
import com.openai.models.chat.completions.ChatCompletionTool;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.ApplicationContextUtil;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.service.dsl.properties.ArrayField;
import io.terminus.trantor2.service.dsl.properties.ConstValue;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.ModelField;
import io.terminus.trantor2.service.dsl.properties.ObjectField;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.dsl.properties.Value;
import io.terminus.trantor2.service.dsl.properties.VarValue;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import io.terminus.trantor2.service.engine.ai.llm.tool.LlmToolCallFieldSchema;
import io.terminus.trantor2.service.engine.impl.component.impl.ModelMetaQueryImpl;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 工具调用执行器抽象类
 */
@Slf4j
public abstract class ToolCallExecution {
    /**
     * 构建工具定义
     *
     * @param tool 工具定义
     * @return ChatCompletionTool 工具描述
     */
    public abstract ChatCompletionTool buildTool(SkillTool tool);

    /**
     * 执行工具调用
     *
     * @param tool          工具定义
     * @param toolName      工具名称
     * @param toolArguments 工具参数
     * @return 工具执行结果
     */
    public abstract String execute(SkillTool tool, String toolName, String toolArguments);

    protected Map<String, JsonValue> buildToolCallInputSchema(List<Field> fields) {
        Map<String, JsonValue> additionalProperty = new HashMap<>();
        Map<String, LlmToolCallFieldSchema> properties = new HashMap<>();
        List<String> required = new ArrayList<>();
        for (Field field : fields) {
            if (Objects.nonNull(field.getRequired()) && field.getRequired()) {
                required.add(field.getFieldKey());
            }
            properties.put(field.getFieldKey(), buildFieldSchema(field, 1));
        }
        additionalProperty.put("type", JsonValue.from("object"));
        additionalProperty.put("properties", JsonValue.from(JsonUtil.toMap(properties)));
        additionalProperty.put("required", JsonValue.from(required));
        additionalProperty.put("additionalProperties", JsonValue.from(false));
        return additionalProperty;
    }

    protected LlmToolCallFieldSchema buildFieldSchema(Field field, Integer depth) {
        Object defaultValue = null;
        if (Objects.nonNull(field.getDefaultValue())) {
            defaultValue = gerDefaultValue(field);
        }
        switch (field.getFieldType()) {
            // @formatter:off
            case Array:
                return LlmToolCallFieldSchema.builder()
                                             .title(field.getFieldKey())
                                             .type("array")
                                             .description(StringUtils.defaultString(field.getDescription(), ""))
                                             .items(buildFieldSchema(((ArrayField)field).getElement(), depth + 1))
                                             .defaultValue(defaultValue)
                                             .build();
            case Object:
            case Pageable:
            case Paging:
            case ConditionGroup:
            case ConditionItems:
                if (Objects.nonNull(((ObjectField)field).getElements()) && !((ObjectField)field).getElements().isEmpty() && depth < 4) {
                    // OpenAI 官方不建议参数层级超过3层，否则模型可能无法准确生成参数
                    Map<String, LlmToolCallFieldSchema> properties = new HashMap<>();
                    List<Field> elements = ((ObjectField)field).getElements();
                    for (Field element : elements) {
                        properties.put(element.getFieldKey(), buildFieldSchema(element, depth + 1));
                    }
                    return LlmToolCallFieldSchema.builder()
                                                 .title(field.getFieldKey())
                                                 .type("object")
                                                 .description(StringUtils.defaultString(field.getDescription(), ""))
                                                 .properties(properties)
                                                 .defaultValue(defaultValue)
                                                 .additionalProperties(false)
                                                 .build();
                }
                return LlmToolCallFieldSchema.builder()
                                             .title(field.getFieldKey())
                                             .type("object")
                                             .description(StringUtils.defaultString(field.getDescription(), ""))
                                             .defaultValue(defaultValue)
                                             .additionalProperties(false)
                                             .build();
            case Model:
                if (Objects.nonNull(((ModelField)field).getRelatedModel().getModelKey()) && depth < 4) {
                    String modelKey = ((ModelField)field).getRelatedModel().getModelKey();
                    List<Field> fields = getRelatedModelFields(modelKey);
                    //defaultValue = ValueFactory.getDefaultValue(fields, AIContext.getContext().getVariableContext());
                    Map<String, LlmToolCallFieldSchema> properties = new HashMap<>();
                    for (Field modelFieldItem : fields) {
                        properties.put(modelFieldItem.getFieldKey(), buildFieldSchema(modelFieldItem, depth + 1));
                    }
                    return LlmToolCallFieldSchema.builder()
                                                 .title(field.getFieldKey())
                                                 .type("object")
                                                 .description(StringUtils.defaultString(field.getDescription(), ""))
                                                 .properties(properties)
                                                 .defaultValue(defaultValue)
                                                 .additionalProperties(false)
                                                 .build();
                }
                return LlmToolCallFieldSchema.builder()
                                             .title(field.getFieldKey())
                                             .type("object")
                                             .description(StringUtils.defaultString(field.getDescription(), ""))
                                             .defaultValue(defaultValue)
                                             .additionalProperties(false)
                                             .build();
            default:
                return LlmToolCallFieldSchema.builder()
                                             .title(field.getFieldKey())
                                             .type("string")
                                             .description(StringUtils.defaultString(field.getDescription(), ""))
                                             .defaultValue(defaultValue)
                                             .build();
            // @formatter:on
        }
    }

    protected ChatCompletionTool buildChatCompletionTool(String toolName, String toolDesc, Map<String, JsonValue> additionalProperties) {
        // @formatter:off
        FunctionParameters functionParameters = FunctionParameters.builder()
                                                                  .additionalProperties(additionalProperties)
                                                                  .build();
        FunctionDefinition functionDefinition = FunctionDefinition.builder()
                                                                  .name(toolName)
                                                                  .description(toolDesc)
                                                                  .parameters(functionParameters)
                                                                  .build();
        // @formatter:on
        return ChatCompletionTool.ofFunction(ChatCompletionFunctionTool.builder().function(functionDefinition).build());
    }

    public Object gerDefaultValue(Field field) {
        if (isVarValue(field.getDefaultValue())) {
            Value value = JsonUtil.fromJson(JsonUtil.toJson(field.getDefaultValue()), Value.class);
            return ValueFactory.getValue(field, value, AIContext.getContext().getVariableContext());
        } else {
            return field.getDefaultValue();
        }
    }

    private boolean isVarValue(Object value) {
        if (value instanceof Map<?, ?> map) {
            if (map.containsKey("type")) {
                return VarValue.class.getSimpleName().equals(map.get("type")) || ConstValue.class.getSimpleName().equals(map.get("type"));
            }
        }
        return false;
    }

    public List<Field> getRelatedModelFields(String modelKey) {
        if (StringUtils.isNotBlank(modelKey)) {
            // 根据 modelKey 获取模型元数据
            try {
                ModelMetaQueryImpl modelMetaQuery = ApplicationContextUtil.getApplicationContext().getBean(ModelMetaQueryImpl.class);
                DataStructMetaCache metaCacheBean = ApplicationContextUtil.getApplicationContext().getBean(DataStructMetaCache.class);
                if (Objects.nonNull(metaCacheBean) && Objects.nonNull(metaCacheBean)) {
                    DataStructNode dataStructNode = metaCacheBean.getModelMeta(TrantorContext.getTeamCode(), null, modelKey);
                    if (dataStructNode != null) {
                        return modelMetaQuery.convertFields(dataStructNode, false);
                    }
                }
            } catch (Exception e) {
                log.error("get related model fields failed", e);
            }
        }
        return new ArrayList<>();
    }
}
