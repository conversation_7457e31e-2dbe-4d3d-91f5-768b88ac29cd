package io.terminus.trantor2.service.engine.impl.expression;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.common.utils.DataLoader;
import io.terminus.trantor2.service.dsl.properties.ConditionGroup;
import io.terminus.trantor2.service.engine.impl.context.SimpleVariableContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * ExpressionEvaluatorTest
 *
 * <AUTHOR> Created on 2023/8/14 17:37
 */
class ExpressionEvaluatorTest {

    @Test
    void testEQ() {

        String ex = "((OUTPUT.data.surveyMission.surveyType #EQ \"VEND\"))";

        Map<String, Object> map = DataLoader.loadMap("/json/ConditionMap.json");
        Object sl = ExpressionEvaluator.evalBoolean(ex, map);

        System.out.println(sl);
    }

    @Test
    void testEQAndLine() {

        String ex = "((surveyMission.surveyType #EQ \"e-sign\"))";

        Map<String, Object> map = MapUtil.of("surveyMission", MapUtil.of("surveyType", "e-sign"));
        Object sl = ExpressionEvaluator.evalBoolean(ex, map);

        System.out.println(sl);
    }

    @Test
    void testISNULL() {

        String ex = "(data #IS_NULL)";

        Map<String, Object> map = new HashMap<>();
        map.put("data", new HashMap<>());
        map.put("data2", new ArrayList<>());

//        System.out.println(ExpressionEvaluator.evalBoolean("(data #IS_NULL)", map));
        System.out.println(ExpressionEvaluator.evalBoolean("(data2 #IS_NULL)", map));

    }

    @Test
    void testStringEQ() {

        String ex = "(data #EQ \"\")";

        Map<String, Object> map = new HashMap<>();
        map.put("data", "");
        map.put("data2", new ArrayList<>());

        boolean result = ExpressionEvaluator.evalBoolean(ex, map);
        System.out.println(result);
        Assertions.assertTrue(result);
    }

    @Test
    void testStringISNULL() {

        String ex = "(data #IS_NULL)";

        Map<String, Object> map = new HashMap<>();
        map.put("data", "");
        map.put("data2", new ArrayList<>());

        boolean result = ExpressionEvaluator.evalBoolean(ex, map);
        System.out.println(result);
        Assertions.assertTrue(result);
    }

    @Test
    void testArrayContainsByConditionGroup() {
        String condition = "{\n" +
                "              \"logicOperator\": \"OR\",\n" +
                "              \"conditions\": [\n" +
                "                {\n" +
                "                  \"logicOperator\": \"AND\",\n" +
                "                  \"conditions\": [\n" +
                "                    {\n" +
                "                      \"id\": \"J2LA3ZBeEP0ThUaIpFwBY\",\n" +
                "                      \"type\": \"ConditionLeaf\",\n" +
                "                      \"leftValue\": {\n" +
                "                        \"type\": \"VarValue\",\n" +
                "                        \"valueType\": \"VAR\",\n" +
                "                        \"fieldType\": \"Array\",\n" +
                "                        \"varValue\": [\n" +
                "                          {\n" +
                "                            \"valueKey\": \"REQUEST\",\n" +
                "                            \"valueName\": \"服务入参\"\n" +
                "                          },\n" +
                "                          {\n" +
                "                            \"valueKey\": \"arr\",\n" +
                "                            \"valueName\": \"arr\"\n" +
                "                          }\n" +
                "                        ],\n" +
                "                        \"element\": {\n" +
                "                          \"id\": null,\n" +
                "                          \"fieldKey\": \"arr\",\n" +
                "                          \"fieldAlias\": \"arr\",\n" +
                "                          \"fieldName\": \"arr\",\n" +
                "                          \"fieldType\": \"Text\"\n" +
                "                        }\n" +
                "                      },\n" +
                "                      \"operator\": \"CONTAINS\",\n" +
                "                      \"rightValue\": {\n" +
                "                        \"type\": \"VarValue\",\n" +
                "                        \"valueType\": \"VAR\",\n" +
                "                        \"fieldType\": \"Text\",\n" +
                "                        \"varValue\": [\n" +
                "                          {\n" +
                "                            \"valueKey\": \"REQUEST\",\n" +
                "                            \"valueName\": \"服务入参\"\n" +
                "                          },\n" +
                "                          {\n" +
                "                            \"valueKey\": \"one\",\n" +
                "                            \"valueName\": \"one\"\n" +
                "                          }\n" +
                "                        ]\n" +
                "                      }\n" +
                "                    }\n" +
                "                  ],\n" +
                "                  \"type\": \"ConditionGroup\",\n" +
                "                  \"id\": \"IZtX3Bc97XLQdDElbMbOw\"\n" +
                "                }\n" +
                "              ],\n" +
                "              \"id\": \"8HCS1QKj5DgsQVJCzx4Uq\",\n" +
                "              \"type\": \"ConditionGroup\"\n" +
                "            }";

        SimpleVariableContext map = new SimpleVariableContext();
        map.setVariable(Lists.newArrayList("REQUEST", "arr"), Lists.newArrayList("one", "two"));
        map.setVariable(Lists.newArrayList("REQUEST", "one"), "one");
        ConditionGroup conditionGroup = JsonUtil.fromJson(condition, ConditionGroup.class);
        String ex = ExpressionEvaluator.parseExpression(conditionGroup);
        System.out.println(ex);
        Assertions.assertEquals("((REQUEST.arr #CONTAINS REQUEST.one))", ex);

        boolean flag = ExpressionEvaluator.evalBoolean(ex, map);
        Assertions.assertTrue(flag);
    }

    @Test
    void testArrayContainsByConditionGroupConstValue() {
        String condition = "{\n" +
                "              \"logicOperator\": \"OR\",\n" +
                "              \"conditions\": [\n" +
                "                {\n" +
                "                  \"logicOperator\": \"AND\",\n" +
                "                  \"conditions\": [\n" +
                "                    {\n" +
                "                      \"id\": \"J2LA3ZBeEP0ThUaIpFwBY\",\n" +
                "                      \"type\": \"ConditionLeaf\",\n" +
                "                      \"leftValue\": {\n" +
                "                        \"type\": \"VarValue\",\n" +
                "                        \"valueType\": \"VAR\",\n" +
                "                        \"fieldType\": \"Array\",\n" +
                "                        \"varValue\": [\n" +
                "                          {\n" +
                "                            \"valueKey\": \"REQUEST\",\n" +
                "                            \"valueName\": \"服务入参\"\n" +
                "                          },\n" +
                "                          {\n" +
                "                            \"valueKey\": \"arr\",\n" +
                "                            \"valueName\": \"arr\"\n" +
                "                          }\n" +
                "                        ],\n" +
                "                        \"element\": {\n" +
                "                          \"id\": null,\n" +
                "                          \"fieldKey\": \"arr\",\n" +
                "                          \"fieldAlias\": \"arr\",\n" +
                "                          \"fieldName\": \"arr\",\n" +
                "                          \"fieldType\": \"Text\"\n" +
                "                        }\n" +
                "                      },\n" +
                "                      \"operator\": \"CONTAINS\",\n" +
                "                      \"rightValue\": {\n" +
                "                        \"type\": \"ConstValue\",\n" +
                "                        \"valueType\": \"CONST\",\n" +
                "                        \"constValue\": \"one\",\n" +
                "                        \"fieldType\": \"Text\"\n" +
                "                      }\n" +
                "                    }\n" +
                "                  ],\n" +
                "                  \"type\": \"ConditionGroup\",\n" +
                "                  \"id\": \"IZtX3Bc97XLQdDElbMbOw\"\n" +
                "                }\n" +
                "              ],\n" +
                "              \"id\": \"8HCS1QKj5DgsQVJCzx4Uq\",\n" +
                "              \"type\": \"ConditionGroup\"\n" +
                "            }";

        SimpleVariableContext map = new SimpleVariableContext();
        map.setVariable(Lists.newArrayList("REQUEST", "arr"), Lists.newArrayList("one", "two"));
        map.setVariable(Lists.newArrayList("REQUEST", "one"), "one");
        ConditionGroup conditionGroup = JsonUtil.fromJson(condition, ConditionGroup.class);
        String ex = ExpressionEvaluator.parseExpression(conditionGroup);
        System.out.println(ex);
        Assertions.assertEquals("((REQUEST.arr #CONTAINS \"one\"))", ex);

        boolean flag = ExpressionEvaluator.evalBoolean(ex, map);
        Assertions.assertTrue(flag);
    }

    @Test
    void testArrayContainsConstValue() {
        String ex = "(data2 #CONTAINS \"11\")";

        Map<String, Object> map = new HashMap<>();
        map.put("data", 11L);
        map.put("data2", Lists.newArrayList("11", "12"));

        Assertions.assertTrue(ExpressionEvaluator.evalBoolean(ex, map));
    }

    @Test
    void testArrayContains() {
        String ex = "(data2 #CONTAINS data)";

        Map<String, Object> map = new HashMap<>();
        map.put("data", 11L);
        map.put("data2", Lists.newArrayList(11, 12));

        Assertions.assertTrue(ExpressionEvaluator.evalBoolean(ex, map));
    }

    @Test
    void testArrayNotContains() {
        String ex = "(data2 #NOT_CONTAINS data)";

        Map<String, Object> map = new HashMap<>();
        map.put("data", 13L);
        map.put("data2", Lists.newArrayList(11, 12));

        Assertions.assertTrue(ExpressionEvaluator.evalBoolean(ex, map));
    }

    @Test
    void testIN() {
        String ex = "(data #IN [data2])";

        Map<String, Object> map = new HashMap<>();
        map.put("data", 11L);
        map.put("data2", Lists.newArrayList(11, 12));

        Assertions.assertTrue(ExpressionEvaluator.evalBoolean(ex, map));
    }

    @Test
    void testNotIN() {
        String ex = "(data #NOT_IN [\"a\"])";

        Map<String, Object> map = new HashMap<>();
        map.put("data", "a");
        map.put("data2", Lists.newArrayList(11, 12));

        Assertions.assertFalse(ExpressionEvaluator.evalBoolean(ex, map));
    }

    @Test
    void testNEQ() {
        String ex = "((GLOBAL.total_score #NEQ 100.1))";

        Map<String, Object> map = new HashMap<>();
        map.put("GLOBAL", MapUtil.of("total_score", 100.1));

        Assertions.assertFalse(ExpressionEvaluator.evalBoolean(ex, map));
    }

    @Test
    void testNEQ2() {
        String ex = "((GLOBAL.vend_detail.changedAfterPersonLinkMd.name #NEQ GLOBAL.vend_detail.changedBeforePersonLinkMd.name))";
        Map<String, Object> map = new HashMap<>();
        map.put("GLOBAL", MapUtil.of("vend_detail", MapUtil.of("changedAfterPersonLinkMd", MapUtil.of("name", "a"))));
//        map.put("GLOBAL", MapUtil.of("vend_detail", MapUtil.of("changedAfterPersonLinkMd", MapUtil.of("name", "a"), "changedBeforePersonLinkMd", MapUtil.of("name", "b"))));
        Assertions.assertTrue(ExpressionEvaluator.evalBoolean(ex, map));
    }

    @Test
    void testDateLT() {
        String ex = "((REQUEST.time #LT SYS.Today))";

        Map<String, Object> map = new HashMap<>();
        map.put("REQUEST", MapUtil.of("time", 1707840000000L));
        map.put("SYS", MapUtil.of("Today", 1708444800000L));

        Assertions.assertTrue(ExpressionEvaluator.evalBoolean(ex, map));
    }

    @Test
    public void testMapIsNull() {
        String ex = "(data.user.id #IS_NULL)";

        Map<String, Object> map = new HashMap<>();
        map.put("data", null);
        map.put("data2", new ArrayList<>());

        boolean result = ExpressionEvaluator.evalBoolean(ex, map);
        System.out.println(result);
        Assertions.assertTrue(result);
    }

    @Test
    public void testMapIsNotNull() {
        String ex = "(data.user.id #IS_NOT_NULL)";

        Map<String, Object> map = new HashMap<>();
        map.put("data", null);
        map.put("data2", new ArrayList<>());

        boolean result = ExpressionEvaluator.evalBoolean(ex, map);
        System.out.println(result);
        Assertions.assertFalse(result);
    }

    @Test
    public void testMultiOr() {
        String condition = "{\n" +
                "              \"logicOperator\": \"OR\",\n" +
                "              \"conditions\": [\n" +
                "                {\n" +
                "                  \"logicOperator\": \"OR\",\n" +
                "                  \"conditions\": [\n" +
                "                    {\n" +
                "                      \"id\": \"Ojm8QM1u_Hci2nvf8WfIW\",\n" +
                "                      \"type\": \"ConditionLeaf\",\n" +
                "                      \"leftValue\": {\n" +
                "                        \"type\": \"VarValue\",\n" +
                "                        \"valueType\": \"VAR\",\n" +
                "                        \"fieldType\": \"Number\",\n" +
                "                        \"varValue\": [\n" +
                "                          {\n" +
                "                            \"valueKey\": \"REQUEST\",\n" +
                "                            \"valueName\": \"服务入参\"\n" +
                "                          },\n" +
                "                          {\n" +
                "                            \"valueKey\": \"a\",\n" +
                "                            \"valueName\": \"a\"\n" +
                "                          },\n" +
                "                          {\n" +
                "                            \"valueKey\": \"a1\",\n" +
                "                            \"valueName\": \"a1\"\n" +
                "                          }\n" +
                "                        ]\n" +
                "                      },\n" +
                "                      \"operator\": \"IS_NOT_NULL\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                      \"id\": \"b0mV3HvrQgP7R0iGWpHWO\",\n" +
                "                      \"type\": \"ConditionLeaf\",\n" +
                "                      \"leftValue\": {\n" +
                "                        \"type\": \"VarValue\",\n" +
                "                        \"valueType\": \"VAR\",\n" +
                "                        \"fieldType\": \"Number\",\n" +
                "                        \"varValue\": [\n" +
                "                          {\n" +
                "                            \"valueKey\": \"REQUEST\",\n" +
                "                            \"valueName\": \"服务入参\"\n" +
                "                          },\n" +
                "                          {\n" +
                "                            \"valueKey\": \"b\",\n" +
                "                            \"valueName\": \"b\"\n" +
                "                          }\n" +
                "                        ]\n" +
                "                      },\n" +
                "                      \"operator\": \"EQ\",\n" +
                "                      \"rightValue\": {\n" +
                "                        \"type\": \"ConstValue\",\n" +
                "                        \"valueType\": \"CONST\",\n" +
                "                        \"constValue\": \"1\",\n" +
                "                        \"fieldType\": \"Number\"\n" +
                "                      }\n" +
                "                    },\n" +
                "                    {\n" +
                "                      \"id\": \"pdbZtk1U8K5e15CjeMKvR\",\n" +
                "                      \"type\": \"ConditionLeaf\",\n" +
                "                      \"leftValue\": {\n" +
                "                        \"type\": \"VarValue\",\n" +
                "                        \"valueType\": \"VAR\",\n" +
                "                        \"fieldType\": \"Number\",\n" +
                "                        \"varValue\": [\n" +
                "                          {\n" +
                "                            \"valueKey\": \"REQUEST\",\n" +
                "                            \"valueName\": \"服务入参\"\n" +
                "                          },\n" +
                "                          {\n" +
                "                            \"valueKey\": \"plusNum\",\n" +
                "                            \"valueName\": \"plusNum\"\n" +
                "                          }\n" +
                "                        ]\n" +
                "                      },\n" +
                "                      \"operator\": \"EQ\",\n" +
                "                      \"rightValue\": {\n" +
                "                        \"type\": \"ConstValue\",\n" +
                "                        \"valueType\": \"CONST\",\n" +
                "                        \"constValue\": \"2\",\n" +
                "                        \"fieldType\": \"Number\"\n" +
                "                      }\n" +
                "                    }\n" +
                "                  ],\n" +
                "                  \"type\": \"ConditionGroup\",\n" +
                "                  \"id\": \"8srpmH-7wdPoXGM16Fp9t\"\n" +
                "                }\n" +
                "              ],\n" +
                "              \"id\": \"s6I76yCuGYblKPnU082MV\",\n" +
                "              \"type\": \"ConditionGroup\"\n" +
                "            }";
        Map<String, Object> map = new HashMap<>();
        map.put("REQUEST", MapUtil.of("plusNum", 2));
        ConditionGroup conditionGroup = JsonUtil.fromJson(condition, ConditionGroup.class);
        String ex = ExpressionEvaluator.parseExpression(conditionGroup);
        System.out.println(ex);

        boolean flag = ExpressionEvaluator.evalBoolean(ex, map);
        Assertions.assertTrue(flag);
    }
}
