package io.terminus.trantor2.service.engine.impl.astservice;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.properties.Pageable;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.impl.gei.StandardExportService;
import io.terminus.trantor2.service.engine.util.DataLoader;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * StdImportServiceTest
 *
 * <AUTHOR> Created on 2024/8/9 11:34
 */
public class StdExportServiceTest extends AbstractExecutorTest {

    @Test
    public void execute() {
        Map<String, Object> args = new HashMap<>();
        args.put("modelKey", "order");
        args.put("request", new Pageable(1, 20));
        args.put("selectFields", null);
        Key serviceKey = Key.of(1L, "ERP_SCM$test_std_export_service");

        when(serviceMetadataQuery.findMeta(serviceKey))
                .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/test_std_export_service.json", ServiceDefinition.class)));

        when(applicationContext.getBean(Mockito.anyString())).thenReturn(new StandardExportService(serviceExecutor));

        Object result = serviceExecutor.execute(serviceKey.getKey(), Arguments.of(1L, args));
        System.out.println(JsonUtil.toJson(result));
    }
}
