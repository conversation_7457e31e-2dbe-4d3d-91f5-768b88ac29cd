package io.terminus.trantor2.module.runtime.cache;

import io.terminus.trantor2.module.runtime.entity.Preference;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class PreferenceCacheKey {
    private String teamCode;
    private String moduleKey;
    private Long userId;
    private String scope;
    private Preference.PreferenceType type;

    @Override
    public String toString() {
        return String.format("%s:%s:%s:%s:%s",
                type,
                userId,
                moduleKey,
                teamCode,
                scope != null ? scope : "default");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PreferenceCacheKey that = (PreferenceCacheKey) o;
        return Objects.equals(userId, that.userId) &&
                Objects.equals(module<PERSON><PERSON>, that.moduleKey) &&
                Objects.equals(teamCode, that.teamCode) &&
                type == that.type &&
                Objects.equals(scope, that.scope);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, moduleKey, teamCode, type, scope);
    }
}
