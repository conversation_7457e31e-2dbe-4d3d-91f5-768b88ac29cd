package io.terminus.trantor2.console.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.ide.repository.PermissionRepo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.MenuTreeMeta;
import io.terminus.trantor2.module.model.dto.MenuTreeSaveRequest;
import io.terminus.trantor2.module.repository.MenuTreeRepo;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.module.service.MenuQueryService;
import io.terminus.trantor2.scene.management.service.SceneManagerQueryService;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class MenuTreeManagerServiceTest {
    @Mock
    private MenuTreeRepo menuTreeRepo;
    @Mock
    private MenuConsoleQueryService menuQueryService;
    @Mock
    private SceneManagerQueryService sceneQueryService;
    @Mock
    private PermissionRepo permissionRepo;

    @InjectMocks
    private MenuTreeManagerService menuTreeManagerService;

    @Test
    @DisplayName("获取菜单树")
    void getTree() {
        Mockito.when(menuQueryService.getMenuTree(Mockito.anyString())).thenReturn(Collections.emptyList());
        List<MenuMeta> menus = menuTreeManagerService.getTree("abc");
        Assertions.assertTrue(menus.isEmpty());
    }

    @Test
    @DisplayName("新建菜单并初始化菜单树")
    public void testSaveTreeWithAddSaveTypeFirstTime() {
        List<MenuMeta> menus = new ArrayList<>();
        MenuMeta meta = new MenuMeta();
        meta.setLabel("hhh");
        meta.setKey("portalCode$curNodeKey");
        meta.setRouteType(MenuMeta.RouteType.None);
        menus.add(meta);
        MenuTreeSaveRequest req = MenuTreeSaveRequest.builder()
                .saveType(MenuTreeSaveRequest.SaveType.ADD)
                .curNodeKey("portalCode$curNodeKey")
                .menus(menus)
                .build();

        Mockito.when(menuTreeRepo.findOneByKey(Mockito.anyString(), Mockito.any(ResourceContext.class))).thenReturn(Optional.empty());
        Assertions.assertDoesNotThrow(() -> menuTreeManagerService.saveTree(req));

        Mockito.verify(menuTreeRepo).create(Mockito.any(MenuTreeMeta.class), Mockito.any(ResourceContext.class));
    }

    @Test
    @DisplayName("基于已有菜单树新建菜单")
    public void testSaveTreeWithAddSaveTypeNotFirstTime() {
        MenuTreeMeta tree = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menuTree.json", MenuTreeMeta.class);
        Mockito.when(menuTreeRepo.findOneByKey(Mockito.anyString(), Mockito.any(ResourceContext.class))).thenReturn(Optional.of(tree));

        List<MenuMeta> menus = new ArrayList<>(tree.getMenus());
        MenuMeta menu1 = new MenuMeta();
        menu1.setRouteType(MenuMeta.RouteType.None);
        menu1.setLabel("1");
        menu1.setKey("portalCode$curNodeKey");
        menus.add(menu1);
        MenuTreeSaveRequest req = MenuTreeSaveRequest.builder()
                .saveType(MenuTreeSaveRequest.SaveType.ADD)
                .curNodeKey("portalCode$curNodeKey")
                .menus(menus)
                .build();

        Assertions.assertDoesNotThrow(() -> menuTreeManagerService.saveTree(req));
        Mockito.verify(menuTreeRepo).update(Mockito.any(MenuTreeMeta.class), Mockito.any(ResourceContext.class));
    }

    @Test
    @DisplayName("编辑菜单")
    public void testSaveTreeWithUpdateSaveTyp() {
        MenuTreeMeta tree = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menuTree.json", MenuTreeMeta.class);
        Mockito.when(menuTreeRepo.findOneByKey(Mockito.anyString(), Mockito.any(ResourceContext.class))).thenReturn(Optional.of(tree));

        List<MenuMeta> menus = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menus.json", new TypeReference<List<MenuMeta>>() {
        });

        menus.get(0).setDescription("hhh");
        MenuTreeSaveRequest req = MenuTreeSaveRequest.builder()
                .saveType(MenuTreeSaveRequest.SaveType.UPDATE)
                .curNodeKey("portalCode$a")
                .menus(menus)
                .build();
        Assertions.assertDoesNotThrow(() -> menuTreeManagerService.saveTree(req));
        Mockito.verify(menuTreeRepo).update(Mockito.any(MenuTreeMeta.class), Mockito.any(ResourceContext.class));
    }

    @Test
    @DisplayName("删除菜单")
    public void testSaveTreeWithDeleteSaveTyp() {
        MenuTreeMeta tree = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menuTree.json", MenuTreeMeta.class);
        Mockito.when(menuTreeRepo.findOneByKey(Mockito.anyString(), Mockito.any(ResourceContext.class))).thenReturn(Optional.of(tree));

        List<MenuMeta> menus = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menus.json", new TypeReference<List<MenuMeta>>() {
        });

        menus.removeIf(it -> it.getKey().equals("portalCode$b"));
        MenuTreeSaveRequest req = MenuTreeSaveRequest.builder()
                .saveType(MenuTreeSaveRequest.SaveType.DELETE)
                .curNodeKey("portalCode$b")
                .menus(menus)
                .build();
        Assertions.assertDoesNotThrow(() -> menuTreeManagerService.saveTree(req));
        Mockito.verify(menuTreeRepo).update(Mockito.any(MenuTreeMeta.class), Mockito.any(ResourceContext.class));
    }

    @DisplayName("ValidateUpdateTree——无任何异常抛出")
    @Test
    public void testValidateUpdateTreeWithValidSaveTypeAndMenuKeys() {
        Map<MenuTreeSaveRequest.SaveType, List<String>> diffs = new HashMap<>();
        List<String> menuKeys = new ArrayList<>();
        menuKeys.add("menu1");
        diffs.put(MenuTreeSaveRequest.SaveType.UPDATE, menuKeys);
        String curNodeKey = "menu1";
        MenuTreeSaveRequest.SaveType saveType = MenuTreeSaveRequest.SaveType.UPDATE;

        Assertions.assertDoesNotThrow(() -> menuTreeManagerService.validateUpdateTree(new MenuTreeMeta(), diffs, saveType, curNodeKey));
    }

    @Test
    @DisplayName("ValidateUpdateTree——无任何改动的更新操作")
    public void testValidateUpdateTreeWithEmptyDiffsAndValidSaveType() {
        Map<MenuTreeSaveRequest.SaveType, List<String>> diffs = new HashMap<>();
        String curNodeKey = "menu1";
        MenuTreeSaveRequest.SaveType saveType = MenuTreeSaveRequest.SaveType.UPDATE;

        Assertions.assertDoesNotThrow(() -> menuTreeManagerService.validateUpdateTree(new MenuTreeMeta(), diffs, saveType, curNodeKey));
    }

    @Test
    @DisplayName("ValidateUpdateTree——无任何改动但实际为创建或删除操作")
    public void testValidateUpdateTreeWithEmptyDiffsAndInvalidSaveType() {
        Map<MenuTreeSaveRequest.SaveType, List<String>> diffs = new HashMap<>();
        String curNodeKey = "menu1";
        MenuTreeSaveRequest.SaveType saveType = MenuTreeSaveRequest.SaveType.ADD;

        Assertions.assertThrows(ValidationException.class, () -> menuTreeManagerService.validateUpdateTree(new MenuTreeMeta(), diffs, saveType, curNodeKey));
    }

    @Test
    @DisplayName("ValidateUpdateTree——保存操作不符合预期")
    public void testValidateUpdateTreeWithInvalidSaveType() {
        Map<MenuTreeSaveRequest.SaveType, List<String>> diffs = new HashMap<>();
        List<String> menuKeys = new ArrayList<>();
        menuKeys.add("menu1");
        diffs.put(MenuTreeSaveRequest.SaveType.UPDATE, menuKeys);
        String curNodeKey = "menu1";
        MenuTreeSaveRequest.SaveType saveType = MenuTreeSaveRequest.SaveType.ADD;

        Assertions.assertThrows(ValidationException.class, () -> menuTreeManagerService.validateUpdateTree(new MenuTreeMeta(), diffs, saveType, curNodeKey));
    }

    @Test
    @DisplayName("ValidateUpdateTree——单操作但改动多个节点")
    public void testValidateUpdateTreeWithMultipleMenuKeys() {
        Map<MenuTreeSaveRequest.SaveType, List<String>> diffs = new HashMap<>();
        List<String> menuKeys = new ArrayList<>();
        menuKeys.add("menu1");
        menuKeys.add("menu2");
        diffs.put(MenuTreeSaveRequest.SaveType.UPDATE, menuKeys);
        String curNodeKey = "menu1";
        MenuTreeSaveRequest.SaveType saveType = MenuTreeSaveRequest.SaveType.UPDATE;
        Assertions.assertThrows(ValidationException.class, () -> menuTreeManagerService.validateUpdateTree(new MenuTreeMeta(), diffs, saveType, curNodeKey));
    }

    @Test
    @DisplayName("ValidateUpdateTree——改动单节点但不是目标节点")
    public void testValidateUpdateTreeWithMismatchedCurNodeKey() {
        Map<MenuTreeSaveRequest.SaveType, List<String>> diffs = new HashMap<>();
        List<String> menuKeys = new ArrayList<>();
        menuKeys.add("menu1");
        diffs.put(MenuTreeSaveRequest.SaveType.UPDATE, menuKeys);
        String curNodeKey = "menu2";
        MenuTreeSaveRequest.SaveType saveType = MenuTreeSaveRequest.SaveType.UPDATE;

        Assertions.assertThrows(ValidationException.class, () -> menuTreeManagerService.validateUpdateTree(new MenuTreeMeta(), diffs, saveType, curNodeKey));
    }

    @Test
    @DisplayName("ValidateUpdateTree——改动包含多操作&多个节点")
    public void testValidateUpdateTreeWithMultipleSaveTypes() {
        Map<MenuTreeSaveRequest.SaveType, List<String>> diffs = new HashMap<>();
        List<String> menuKeys1 = new ArrayList<>();
        menuKeys1.add("menu1");
        List<String> menuKeys2 = new ArrayList<>();
        menuKeys2.add("menu2");
        diffs.put(MenuTreeSaveRequest.SaveType.UPDATE, menuKeys1);
        diffs.put(MenuTreeSaveRequest.SaveType.ADD, menuKeys2);
        String curNodeKey = "menu1";
        MenuTreeSaveRequest.SaveType saveType = MenuTreeSaveRequest.SaveType.UPDATE;
        Assertions.assertThrows(ValidationException.class, () -> menuTreeManagerService.validateUpdateTree(new MenuTreeMeta(), diffs, saveType, curNodeKey));
    }
}