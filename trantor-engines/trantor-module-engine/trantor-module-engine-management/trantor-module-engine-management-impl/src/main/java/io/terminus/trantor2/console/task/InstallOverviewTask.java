package io.terminus.trantor2.console.task;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.meta.api.dto.EditOpRequest;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.meta.api.dto.MoveTargetType;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.blob.MetaBlob;
import io.terminus.trantor2.meta.blob.MetaBlobRepo;
import io.terminus.trantor2.meta.index.MetaIndexAsset;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.meta.management.service.EditorMetaEditService;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.objects.tree.folder.Folder;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.meta.ModuleType;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.task.TaskOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 **/
@Component
public class InstallOverviewTask extends BaseTask<InstallOverviewTask.Options> {

    @Autowired
    private ModuleRepo moduleRepo;
    @Autowired
    private MetaQueryService metaQueryService;
    @Autowired
    private EditorMetaEditService editorMetaEditService;
    @Autowired
    private MetaBlobRepo metaBlobRepo;
    @Autowired
    private MetaIndexAssetRepo metaIndexAssetRepo;

    @Override
    public void exec(InstallOverviewTask.Options opts, TaskOutput output, TaskContext ctx) {

        // 初始概念模块
        boolean exist = initOverviewModule(ctx);
        if (exist) {
            return;
        }

        // 初始根文件夹
        initRootFolder(ctx);

        // 初始文件夹，用于后续保存产品概念
        initOverviewFolder(ctx);
    }

    private boolean initOverviewModule(TaskContext ctx) {
        ResourceContext resourceCtx = ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId());
        Optional<ModuleMeta> moduleMeta = moduleRepo.findOneByKey(KeyUtil.SYS_OVERVIEW_KEY, resourceCtx);
        if (moduleMeta.isPresent()) {
            return true;
        }

        ModuleMeta initOverview = new ModuleMeta();
        initOverview.setName("流程中心");
        initOverview.setKey(KeyUtil.SYS_OVERVIEW_KEY);
        initOverview.setResourceProps(new ModuleMeta.ModuleProps());
        initOverview.getResourceProps().setType(ModuleType.Overview);

        MetaTreeNode root = metaQueryService.queryInTeam(ctx.getTeamId()).findRoot().orElseThrow(() -> new TrantorRuntimeException("root node not found"));
        initOverview.setParentKey(root.getKey());

        // 初始模块
        moduleRepo.create(initOverview, resourceCtx);
        return false;
    }

    private void initRootFolder(TaskContext ctx) {
        String moduleKey = metaBlobRepo.findOne(ctx.getTeamId(), MetaBlob.Key.class, KeyUtil.SYS_OVERVIEW_KEY)
                .map(MetaBlob.Key::getKey)
                .orElseThrow(() -> new IllegalArgumentException("module not found"));
        List<MetaIndexAsset.Base> firstLvNodes = metaIndexAssetRepo.find(ctx.getTeamId(), MetaIndexAsset.Base.class,
                Cond.and(
                        Cond.moduleOf(moduleKey),
                        Field.type().in(
                                MetaType.FolderRoot.name(),
                                MetaType.MenuRoot.name(),
                                MetaType.ErrorCodeRoot.name()
                        )

                ));

        String folderRootKey = firstLvNodes.stream()
                .filter(it -> Objects.equals(it.getType(), Folder.ROOT_TYPE))
                .findFirst().map(MetaIndexAsset.Base::getKey).orElse(null);
        if (folderRootKey == null) {
            editorMetaEditService.initFirstLevelNodeOne(Folder.ROOT_TYPE, "__folder__", "FolderRoot", KeyUtil.SYS_OVERVIEW_KEY, KeyUtil.SYS_OVERVIEW_KEY, EditUtil.newCtx(ctx.getTeamId(), ctx.getUserId()));
        }
    }

    private void initOverviewFolder(TaskContext ctx) {
        MetaTreeNode metaTreeNode = new MetaTreeNode();
        metaTreeNode.setType(Folder.TYPE);
        metaTreeNode.setKey(KeyUtil.newKeyUnderModule(KeyUtil.SYS_OVERVIEW_KEY, "__overview__"));
        metaTreeNode.setName("产品概念");
        metaTreeNode.setParentKey(KeyUtil.newKeyUnderModule(KeyUtil.SYS_OVERVIEW_KEY, "__folder__"));

        EditOpRequest req = EditUtil.createNodeOp(metaTreeNode, new MoveTarget(metaTreeNode.getParentKey(), MoveTargetType.ChildLast));
        editorMetaEditService.submitOp(EditUtil.newCtx(ctx.getTeamId(), TrantorContext.getCurrentUserId()), req);
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static final class Options extends BaseTask.Options {
    }

}
