package io.terminus.trantor2.model.ts.impl;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.model.common.model.request.CreateObject;
import io.terminus.trantor2.model.common.model.request.IdQueryRequest;
import io.terminus.trantor2.model.common.model.request.Select;
import io.terminus.trantor2.model.runtime.api.dml.DataStructDataApi;
import io.terminus.trantor2.model.runtime.api.transaction.ErpTransactional;
import io.terminus.trantor2.model.runtime.meta.transaction.TrantorTransactional;
import io.terminus.trantor2.model.ts.api.AnnotationTranService;
import io.terminus.trantor2.model.ts.api.SubAnnotationTranService;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;

import java.util.*;

@Service
public class SubAnnotationTranServiceImpl extends BaseServiceImpl implements SubAnnotationTranService {
    public static final String test_c_alias = "testAdd3$test_c";
    @Autowired
    private DataStructDataApi dataApi;

    @Override
    public void transactionPropagateTest(boolean needRollback) {
        mockData(test_c_alias, 111L, "zzz");

        // 模拟事务回滚
        if (needRollback) {
            int a = 1 / 0;
        }
    }

    @Override
    public void testBatchAddNestedMethodDataValidate(boolean needRollback) {
        _testBatchAddDataValidate(111L, needRollback, "testAdd$dump_search");
        _testBatchAddDataValidate(222L, needRollback, "testAdd$dump_search");
    }

    @TrantorTransactional
    @Override
    public void remoteTest() {
        mockData(test_c_alias, 111L, "zzz");
        mockData(test_c_alias, 111L, "zzz");
    }

    @TrantorTransactional(propagation = Propagation.REQUIRED)
    @Override
    public void remoteREQUIRED(boolean innerRollback) {
        mockData(test_c_alias, 111L, "zzz");

        if (innerRollback) {
            int a = 1 / 0;
        }
    }

    @TrantorTransactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void remoteREQUIRES_NEW(boolean innerRollback) {
        mockData(test_c_alias, 111L, "zzz");
        if (innerRollback) {
            int a = 1 / 0;
        }
    }

    @TrantorTransactional(propagation = Propagation.NESTED)
    @Override
    public void remoteNESTED(boolean innerRollback) {
        mockData(test_c_alias, 111L, "zzz");
        if (innerRollback) {
            int a = 1 / 0;
        }
    }
}
