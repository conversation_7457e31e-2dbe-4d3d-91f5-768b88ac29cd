package io.terminus.trantor2.model.engine.dml.orm.sql;


import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.GeneralModel;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;

import java.util.HashMap;
import java.util.Map;

/**
 * InsertSQL
 *
 * <AUTHOR> Created on 2023/6/11 00:44
 */
public class InsertSQL extends AbstractSQL {
    public InsertSQL(SqlBuilder<?> builder) {
        super(builder);
    }

    @Override
    protected void render() {
        SQL().INSERT_INTO(table()).VALUES(insertValues(generalModel));
    }

    private Map<String, Object> insertValues(GeneralModel generalModel) {
        Map<String, DataStructFieldNode> fieldMapping = getModelMeta(generalModel).toFieldMap();
        Map<String, Object> values = new HashMap<>(generalModel.getValue().size());
        generalModel.getValue().forEach((field, v) -> {
            if (v != null) {
                values.put(formatColumnName(field, fieldMapping), formatParameterPlaceholder(v));
            }
        });
        return values;
    }
}
