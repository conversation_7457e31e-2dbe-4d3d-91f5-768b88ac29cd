package io.terminus.trantor2.permission.management.impl.task.snapshot;

import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.iam.api.response.permission.v2.PermissionAssign;
import io.terminus.iam.api.response.role.Role;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionAssignService;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionResourceService;
import io.terminus.trantor2.iam.service.TrantorIAMRoleService;
import io.terminus.trantor2.ide.repository.ApiMetaRepo;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.meta.exception.MetaTaskException;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskRunRepo;
import io.terminus.trantor2.meta.management.task.TaskService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.module.util.MenuUtils;
import io.terminus.trantor2.permission.ApiMeta;
import io.terminus.trantor2.permission.api.common.service.AccessControlQueryService;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.impl.common.util.PermissionUtils;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.SceneRepo;
import io.terminus.trantor2.scene.utils.SceneUtils;
import io.terminus.trantor2.task.TaskOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024/12/25 10:11
 **/
@TaskService(displayName = "构建角色授权快照(单一门户)", visible = true)
public class PermissionSnapshotForSinglePortalRebuildTask extends AbstractPermissionSnapshotTask<PermissionSnapshotForSinglePortalRebuildTask.Options> {

    private final MenuConsoleQueryService menuConsoleQueryService;
    private final SceneRepo sceneRepo;
    private final AccessControlQueryService accessControlQueryService;

    public PermissionSnapshotForSinglePortalRebuildTask(ApiMetaRepo apiMetaRepo,
                                                        TrantorIAMRoleService iamRoleService,
                                                        TrantorIAMPermissionResourceService iamPermissionResourceService,
                                                        TrantorIAMPermissionAssignService iamPermissionAssignService,
                                                        PortalToIamAppConverter portalToIamAppConverter,
                                                        TaskRunRepo taskRunRepo,
                                                        MenuConsoleQueryService menuConsoleQueryService,
                                                        SceneRepo sceneRepo,
                                                        AccessControlQueryService accessControlQueryService) {
        super(apiMetaRepo, iamRoleService, iamPermissionResourceService, iamPermissionAssignService, portalToIamAppConverter, taskRunRepo);
        this.menuConsoleQueryService = menuConsoleQueryService;
        this.sceneRepo = sceneRepo;
        this.accessControlQueryService = accessControlQueryService;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        private String portalCode;
    }

    @Override
    public void preCheck(Options opts, TaskContext ctx) {
        if (StringUtils.isBlank(opts.getPortalCode())) {
            throw new MetaTaskException("portalCode is required");
        }
    }

    @Override
    public String showOpts(Options opts) {
        return " (门户: " + opts.getPortalCode() + ")";
    }

    @Override
    public void exec(Options opts, TaskOutput output, TaskContext ctx) {
        output.log("portalCode: " + opts.getPortalCode());

        // 查询所有门户角色信息
        Map<String, Role> idToRole = getAllPortalRoles().stream()
                .collect(Collectors.toMap(role -> String.valueOf(role.getId()), Function.identity()));
        if (MapUtils.isEmpty(idToRole)) {
            output.log("No any portal role");
            return;
        }

        // 权限快照
        PermissionSnapshot permissionSnapshot = new PermissionSnapshot();

        // 获取指定角色的权限授权信息
        Collection<PermissionAssign> rolePermissionAssigns = findRolePermissionAssigns(opts.getPortalCode(), idToRole.keySet());
        if (CollectionUtils.isNotEmpty(rolePermissionAssigns)) {
            // 查询角色-权限-授权状态信息
            Map<String, Map<String, AuthorizationEffect>> roleIdToPermKeyToEffect = getRoleIdToPermissionKeyToAuthEffect(rolePermissionAssigns);
            // 查询角色-资源-授权状态信息
            Map<String, Map<String, AuthorizationEffect>> roleIdToResourceKeyToEffect = getRoleIdToResourceKeyToAuthEffect(ctx, opts, roleIdToPermKeyToEffect);
            // 查询门户接口访问控制授权信息
            Map<String, Map<String, AuthorizationEffect>> roleIdToApiMetaToEffect = getRoleIdToApiMetaToAuthEffect(opts, output, ctx, opts.getPortalCode(), roleIdToPermKeyToEffect);

            // 删除 AuthorizationEffect = NONE 的对象
            removeIfEffectIsNone(roleIdToResourceKeyToEffect);
            removeIfEffectIsNone(roleIdToPermKeyToEffect);
            removeIfEffectIsNone(roleIdToApiMetaToEffect);

            permissionSnapshot.setRoleIdToResourceKeyToEffect(roleIdToResourceKeyToEffect);
            permissionSnapshot.setRoleIdToPermKeyToEffect(roleIdToPermKeyToEffect);
            permissionSnapshot.setRoleIdToApiMetaToEffect(roleIdToApiMetaToEffect);
        }

        output.resultData(permissionSnapshot);
    }

    private Map<String, Map<String, AuthorizationEffect>> getRoleIdToApiMetaToAuthEffect(Options opts, TaskOutput output, TaskContext ctx,
                                                                                         String portalCode,
                                                                                         Map<String, Map<String, AuthorizationEffect>> roleIdToPermKeyToEffect) {
        Map<String, Map<String, AuthorizationEffect>> roleIdToResourceKeyToEffect = new HashMap<>();
        List<ApiMeta> apiMetaList = getPortalApiMetas(opts, output, ctx, portalCode);
        if (CollectionUtils.isNotEmpty(apiMetaList)) {
            Map<String, String> apiMetaKeyToPermissionKeyMap = accessControlQueryService.getApiMetaKeyToPermissionKeyMap(apiMetaList);
            for (ApiMeta apiMeta : apiMetaList) {
                String permKey = apiMetaKeyToPermissionKeyMap.get(apiMeta.getKey());
                if (StringUtils.isNotBlank(permKey)) {
                    roleIdToPermKeyToEffect.forEach((roleId, permKeyToEffect) -> {
                        roleIdToResourceKeyToEffect.putIfAbsent(roleId, new TreeMap<>());
                        roleIdToResourceKeyToEffect.get(roleId)
                                .put(apiMeta.getResourceProps().getUniqueKey(), permKeyToEffect.getOrDefault(permKey, AuthorizationEffect.NONE));
                    });
                }
            }
        }
        return roleIdToResourceKeyToEffect;
    }

    private Map<String, Map<String, AuthorizationEffect>> getRoleIdToResourceKeyToAuthEffect(@NotNull TaskContext ctx,
                                                                                             @NotNull Options opts,
                                                                                             @NotEmpty Map<String, Map<String, AuthorizationEffect>> roleIdToPermKeyToEffect) {
        // 查询门户菜单信息
        List<MenuMeta> menuMetas = menuConsoleQueryService.getMenuTree(opts.getPortalCode());
        List<MenuMeta> flatMenuMetaList = new ArrayList<>();
        MenuUtils.flatMenuMetaListRecursively(menuMetas, flatMenuMetaList);
        List<MenuMeta> routeMenuMetaList = flatMenuMetaList.stream().filter(MenuMeta::hasRoute).collect(Collectors.toList());
        // 菜单key to 菜单权限项key
        Map<String, String> menuKeyToPermissionKey = routeMenuMetaList.stream().collect(Collectors.toMap(MenuMeta::getKey, MenuMeta::getPermissionKey));
        // 查询菜单-场景信息
        Map<String, String> menuKeyToSceneKey = MenuUtils.getMenuToSceneMap(routeMenuMetaList);
        // sceneKey to sceneMeta
        Set<String> sceneKeys = menuKeyToSceneKey.values().stream().filter(Objects::nonNull).collect(Collectors.toSet());
        Map<String, SceneMeta> keyToScene = SceneUtils.findLightSceneMap(metaQueryService, sceneRepo, sceneKeys);
        // 查询场景-视图信息
        Map<String, Collection<DataManagerView>> sceneKeyToViews = new HashMap<>();
        keyToScene.forEach((sceneKey, sceneMeta) -> {
            if (Objects.nonNull(sceneMeta)) {
                if (!SceneType.DATA.equals(sceneMeta.getType())) {
                    return;
                }
                List<DataManagerView> views = ((DataManagerSceneConfig) sceneMeta.getSceneConfig()).getViews();
                if (CollectionUtils.isNotEmpty(views)) {
                    sceneKeyToViews.put(sceneKey, views);
                }
            }
        });

        // 【批量查询视图的资源权限结构
        MetaEditAndQueryContext metaEditAndQueryContext = EditUtil.newCtx(ctx.getTeamId(), ctx.getUserId());
        Collection<String> allViewKeys = sceneKeyToViews.values().stream().flatMap(Collection::stream).map(DataManagerView::getKey).collect(Collectors.toSet());
        List<ViewPermissionDTO> viewPermissions = metaQueryService.findViewPermissions(metaEditAndQueryContext, opts.getPortalCode(), new ArrayList<>(allViewKeys));

        // 批量查询视图引用权限项：viewKey -> set of view permissionKey
        Map<String, String> viewKeyToOwnPermissionKey = new HashMap<>();
        Map<String, Set<String>> viewKeyToRefPermissionKeys = new HashMap<>();
        PermissionUtils.getViewRefPermissionKeys(viewPermissions, viewKeyToOwnPermissionKey, viewKeyToRefPermissionKeys);

        // 批量查询视图所有按钮引用权限项：viewKey -> buttonKey -> set of button permissionKey
        Map<String, Map<String, String>> viewKeyToButtonKeyToOwnPermissionKeyMap = new HashMap<>();
        Map<String, Map<String, Set<String>>> viewKeyToButtonKeyToRefPermissionKeysMap = new HashMap<>();
        PermissionUtils.getButtonRefPermissionKeys(viewPermissions, viewKeyToButtonKeyToOwnPermissionKeyMap, viewKeyToButtonKeyToRefPermissionKeysMap);

        // 查询角色-资源-授权状态信息
        Map<String, Map<String, AuthorizationEffect>> roleIdToResourceKeyToEffect = new HashMap<>();

        roleIdToPermKeyToEffect.forEach((roleId, permKeyToEffect) -> {
            roleIdToResourceKeyToEffect.putIfAbsent(roleId, new TreeMap<>());
            // 记录菜单授权快照
            if (MapUtils.isNotEmpty(menuKeyToPermissionKey)) {
                menuKeyToPermissionKey.forEach((menuKey, menuPermKey) -> {
                    roleIdToResourceKeyToEffect.get(roleId)
                            .put(menuKey, permKeyToEffect.getOrDefault(menuPermKey, AuthorizationEffect.NONE));
                });
            }
            // 记录视图授权快照
            if (MapUtils.isNotEmpty(viewKeyToOwnPermissionKey)) {
                viewKeyToOwnPermissionKey.forEach((viewKey, viewOwnPermKey) -> {
                    roleIdToResourceKeyToEffect.get(roleId)
                            .put(viewKey, permKeyToEffect.getOrDefault(viewOwnPermKey, AuthorizationEffect.NONE));
                    // 记录按钮授权快照
                    if (MapUtils.isNotEmpty(viewKeyToButtonKeyToOwnPermissionKeyMap)) {
                        Map<String, String> buttonKeyToOwnPermissionKey = viewKeyToButtonKeyToOwnPermissionKeyMap.get(viewKey);
                        if (MapUtils.isNotEmpty(buttonKeyToOwnPermissionKey)) {
                            buttonKeyToOwnPermissionKey.forEach((buttonKey, buttonOwnPermKey) -> {
                                if (StringUtils.isNotBlank(buttonOwnPermKey)) {
                                    roleIdToResourceKeyToEffect.get(roleId)
                                            .put(buttonKey, permKeyToEffect.getOrDefault(buttonOwnPermKey, AuthorizationEffect.NONE));
                                } else {
                                    Map<String, Set<String>> buttonKeyToRefPermissionKeys = viewKeyToButtonKeyToRefPermissionKeysMap.get(viewKey);
                                    if (MapUtils.isNotEmpty(buttonKeyToRefPermissionKeys)) {
                                        Set<String> buttonRefPermissionKeys = buttonKeyToRefPermissionKeys.getOrDefault(buttonKey, Collections.emptySet());
                                        Set<AuthorizationEffect> authorizationEffects = buttonRefPermissionKeys.stream()
                                                .map(permKeyToEffect::get)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toSet());
                                        roleIdToResourceKeyToEffect.get(roleId)
                                                .put(buttonKey, PermissionUtils.determineAuthorizationEffectWithinSingleRole(authorizationEffects));
                                    }
                                }
                            });
                        }
                    }
                });
            }
        });
        return roleIdToResourceKeyToEffect;
    }

    private void removeIfEffectIsNone(Map<String, Map<String, AuthorizationEffect>> roleIdToAuthorizationEffectMap) {
        roleIdToAuthorizationEffectMap.forEach((roleId, authorizationEffectMap) -> {
            Iterator<Map.Entry<String, AuthorizationEffect>> iterator = authorizationEffectMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, AuthorizationEffect> next = iterator.next();
                if (Objects.isNull(next.getValue()) || AuthorizationEffect.NONE.equals(next.getValue())) {
                    iterator.remove();
                }
            }
        });
    }
}
