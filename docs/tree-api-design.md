# Tree API 体系设计文档

## 1. 概述

Trantor 系统中的 Tree API 体系为不同的使用场景提供了多种树形数据查询接口。本文档描述了完整的 Tree API 架构，包括各接口的设计目标、使用场景和实现细节。

## 2. Tree API 体系架构

### 2.1 核心接口列表

| 接口 | URL | 用途 | 主要场景 |
|------|-----|------|----------|
| **tree** | `/api/trantor/console/resource-node/tree` | IDE目录树 | IDE左侧文件浏览器 |
| **select-tree** | `/api/trantor/console/resource-node/select-tree` | 资源选择树 | 编辑时的资源选择框 |
| **usage-tree** | `/api/trantor/console/resource-node/usage-tree` | 依赖关系树 | 查看资源引用关系 |
| **filter-tree-unused** | `/api/trantor/console/resource-node/filter-tree-unused` | 未使用资源树 | 清理无用资源 |

### 2.2 返回数据结构

所有 Tree API 都返回统一的 `TempTree` 结构：

```json
{
  "modules": [
    {
      "key": "模块键",
      "name": "模块名称", 
      "children": [
        {
          "key": "资源键",
          "type": "资源类型",
          "subType": "子类型",
          "name": "资源名称",
          "path": "路径",
          "isInner": false,
          "extended": false,
          "children": [...]
        }
      ]
    }
  ]
}
```

## 3. 各接口详细设计

### 3.1 tree 接口

**目标**: 为 IDE 提供标准的目录树结构

#### 接口规格
- **URL**: `/api/trantor/console/resource-node/tree`
- **Method**: GET
- **参数**: 无
- **特点**: 
  - 自动使用 `TrantorContext.getModuleKey()` 获取当前模块
  - 只返回当前模块的目录和文件结构
  - 适合 IDE 左侧文件浏览器使用

#### 使用示例
```http
GET /api/trantor/console/resource-node/tree
```

### 3.2 select-tree 接口（新增）

**目标**: 为编辑场景提供灵活的资源选择功能

#### 接口规格
- **URL**: `/api/trantor/console/resource-node/select-tree`
- **Method**: GET
- **参数**:

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| mode | ModuleRelationQueryMode | 否 | CURRENT | 查询模式：CURRENT/RELATION/ALL |
| queryAccessLevel | QueryAccessLevel | 否 | RESTRICTED | 访问级别：RESTRICTED/UNRESTRICTED |
| queryTemplate | QueryTemplate | 否 | ALL | 查询模板：ALL/MODEL_ALL/SERVICE_ALL等 |
| queryParams | Map<String,String> | 否 | - | 查询参数，key-value形式 |

#### 核心特性
- **模块范围控制**: 通过 `mode` 参数控制查询范围
- **资源类型过滤**: 通过 `queryTemplate` 过滤特定类型资源
- **访问权限控制**: 通过 `queryAccessLevel` 控制访问级别
- **自动上下文**: 从 `TrantorContext.getModuleKey()` 获取当前模块

#### 使用示例

1. **选择当前模块的所有资源**:
```http
GET /api/trantor/console/resource-node/select-tree
```

2. **选择所有模块的模型资源**:
```http
GET /api/trantor/console/resource-node/select-tree?mode=ALL&queryTemplate=MODEL_ALL
```

3. **选择相关模块的公开服务**:
```http
GET /api/trantor/console/resource-node/select-tree?mode=RELATION&queryTemplate=SERVICE_ALL&queryAccessLevel=RESTRICTED
```

### 3.3 usage-tree 接口

**目标**: 展示资源的依赖和被依赖关系

#### 接口规格
- **URL**: `/api/trantor/console/resource-node/usage-tree`
- **Method**: GET
- **参数**:
  - `key` (必填): 目标资源键
  - `innerKey` (可选): 内部资源键
  - `direction` (可选): 查询方向，默认 Backward

#### 使用示例
```http
GET /api/trantor/console/resource-node/usage-tree?key=module.model.User&direction=Backward
```

### 3.4 filter-tree-unused 接口

**目标**: 查找未使用的资源，用于清理

#### 接口规格
- **URL**: `/api/trantor/console/resource-node/filter-tree-unused`
- **Method**: GET
- **参数**:
  - `path` (可选): 过滤路径

#### 使用示例
```http
GET /api/trantor/console/resource-node/filter-tree-unused?path=models
```

## 4. 参数详解

### 4.1 ModuleRelationQueryMode

| 值 | 说明 | 使用场景 |
|----|------|----------|
| CURRENT | 仅查询当前模块 | 编辑当前模块资源时的选择 |
| RELATION | 查询当前模块及其相关联的模块 | 选择可访问的相关模块资源 |
| ALL | 查询所有模块 | 全局资源选择，如管理员操作 |

### 4.2 QueryAccessLevel

| 值 | 说明 | 适用场景 |
|----|------|----------|
| RESTRICTED | 受限访问，只能访问公开的相关模块资源 | 普通用户的跨模块资源选择 |
| UNRESTRICTED | 不受限访问，可以访问所有相关模块资源 | 管理员或特殊权限的资源选择 |

### 4.3 QueryTemplate

支持多种预定义查询模板：

| 模板 | 说明 | 使用场景 |
|------|------|----------|
| ALL | 查询所有类型 | 通用资源选择 |
| MODEL_ALL | 只查询模型 | 选择数据模型时 |
| SERVICE_ALL | 只查询服务 | 选择业务服务时 |
| SEARCHABLE_ALL | 只查询可搜索类型 | 搜索功能的资源选择 |
| SCENE_ALL | 只查询场景 | 选择页面场景时 |

## 5. 使用场景对比

### 5.1 IDE 文件浏览器
- **使用接口**: `tree`
- **特点**: 简单、固定、只显示当前模块
- **示例**: VSCode 左侧文件夹结构

### 5.2 编辑器内资源选择
- **使用接口**: `select-tree`
- **特点**: 灵活、可过滤、支持跨模块
- **示例**: 
  - 编辑服务时选择数据模型
  - 编辑页面时选择业务服务
  - 配置引用时选择目标资源

### 5.3 依赖关系分析
- **使用接口**: `usage-tree`
- **特点**: 关系导向、支持正向/反向查询
- **示例**: 
  - 查看模型被哪些服务使用
  - 查看服务依赖哪些模型

### 5.4 资源清理
- **使用接口**: `filter-tree-unused`
- **特点**: 发现未使用资源、支持批量操作
- **示例**: 定期清理无用的模型和服务

## 6. 实现架构

### 6.1 Controller 层
所有 Tree API 都在 `ResourceNodeController` 中实现，提供统一的入口。

### 6.2 Service 层
- `ResourceNodeManagerService`: 处理 `select-tree` 的业务逻辑
- `MetaUsageResolver`: 处理 `tree`、`usage-tree`、`filter-tree-unused` 的核心逻辑

### 6.3 数据流
1. **请求解析**: Controller 解析请求参数
2. **上下文获取**: 从 `TrantorContext` 获取当前用户和模块信息
3. **权限校验**: 根据访问级别检查权限
4. **数据查询**: 调用相应的服务获取数据
5. **结果构建**: 构建统一的 `TempTree` 结构返回

## 7. 扩展计划

### 7.1 短期扩展
- 在 `MetaUsageResolver` 中增加对 `QueryTemplate` 和 `QueryAccessLevel` 的原生支持
- 优化树结构构建的性能
- 增加缓存机制

### 7.2 长期扩展
- 支持更复杂的查询条件组合
- 支持树结构的增量更新
- 支持客户端缓存策略
- 支持自定义树结构展示

## 8. 迁移建议

### 8.1 现有接口影响
- **tree 接口**: 保持不变，继续用于 IDE 场景
- **relation 接口**: 逐步迁移资源选择场景到 `select-tree`
- **paging 接口**: 逐步迁移资源选择场景到 `select-tree`

### 8.2 迁移步骤
1. **第一阶段**: 新功能使用 `select-tree` 接口
2. **第二阶段**: 逐步将现有的资源选择功能迁移到 `select-tree`
3. **第三阶段**: 评估是否废弃重复功能的旧接口

## 9. 最佳实践

### 9.1 接口选择原则
- **文件浏览**: 使用 `tree`
- **资源选择**: 使用 `select-tree`
- **关系分析**: 使用 `usage-tree`
- **清理维护**: 使用 `filter-tree-unused`

### 9.2 性能优化建议
- 根据实际需要选择合适的查询范围（mode）
- 使用具体的查询模板而非 ALL
- 合理使用访问级别控制
- 考虑客户端缓存

### 9.3 错误处理
- 当 `moduleKey` 为空时会抛出 `TrantorRuntimeException`
- 权限不足时返回受限的结果集
- 无效参数时返回相应的错误信息

---

**作者**: Claude Code  
**版本**: 1.0  
**日期**: 2025-07-02  
**更新**: 包含完整的 Tree API 体系设计