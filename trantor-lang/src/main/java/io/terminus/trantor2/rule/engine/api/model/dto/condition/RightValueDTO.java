package io.terminus.trantor2.rule.engine.api.model.dto.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 右值
 * <AUTHOR>
 * @createTime 2023/3/22 2:35 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RightValueDTO implements Serializable {
    private static final long serialVersionUID = 6853360314596114114L;

    /**
     * 值类型。CONSTANT-固定值、MODEL-模型、FACTOR-因子、FUNCTION-函数
     */
    private String valueType;

    /**
     * 字段类型。
     * Text-文本（RichText-富文本、Email-邮箱）
     * Boolean-布尔值
     * Number-数字
     * DateTime-日期
     * Time-时间
     * Enum-枚举
     */
    private String fieldType;

    /**
     * 值。值类型为固定值时必填
     */
    private String value;

    /**
     * 函数。值类型为函数时必填
     */
    private String function;

    /**
     * 因子值。值类型为因子时必填
     */
    private FactorValueDTO factor;

    /**
     * 模型值。值类型为模型时必填
     */
    private ModelValueDTO model;

}

