package io.terminus.trantor2.meta.resource.ext.jsonfunc.params;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetConstValue extends SetJsonValueParams {
    private JsonNode constant;

    @Override
    public JsonNode getValue(Map<String, JsonNode> key2ObjectNodMap) {
        return constant;
    }
}
