package io.terminus.trantor2.service.dsl.properties.ai;

import io.terminus.trantor2.service.dsl.properties.SkillTool;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
public class ProprietaryTool extends SkillTool {
    private static final long serialVersionUID = 8039929556998969134L;

    /**
     * 当为true时，该工具的结果会直接作为最终输出结果
     */
    private boolean finalOutput;

    private ToolVisible toolVisible;
}
