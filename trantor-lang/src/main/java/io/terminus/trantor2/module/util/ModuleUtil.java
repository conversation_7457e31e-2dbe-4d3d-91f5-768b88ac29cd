package io.terminus.trantor2.module.util;

import io.terminus.trantor2.meta.util.KeyUtil;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/8/14 10:05 AM
 **/
public class ModuleUtil {

    public static String jointKey(String k1, String k2) {
        return k1 + "-" + k2;
    }

    /**
     * 微服务名称
     */
    public static String microServiceKey(String teamCode, String moduleKey) {
        String microServiceKey = KeyUtil.originalKey(moduleKey) + "-" + teamCode;
        return microServiceKey.replace('_', '-');
    }
}
