package io.terminus.trantor2.meta.management.task.primitive;

import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.meta.api.dto.Manifest;
import io.terminus.trantor2.meta.editor.util.ObjectUtil;
import io.terminus.trantor2.meta.exception.MetaTaskException;
import io.terminus.trantor2.meta.management.service.ISystemModuleManagerService;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.object.MetaObjectV2;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.service.OSSService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static io.terminus.trantor2.module.util.OSSConstant.CONSOLE_FILE_PREFIX;
import static io.terminus.trantor2.module.util.OSSConstant.PRIVATE_PREFIX;

@Slf4j
@Component
public final class SyncSystemAppTask extends BaseTask<SyncSystemAppTask.Options> {
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
    }

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired(required = false)
    private OSSService ossService;

    @Autowired
    private ISystemModuleManagerService systemModuleManagerService;

    @Override
    public void preCheck(Options opts, TaskContext ctx) {
    }

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        // step1: download the newest version
        byte[] bytes = systemModuleManagerService.downloadLatestResource();
        //bytes write to path
        Path path = null;
        try {
            path = Files.createTempFile("trantor-meta-system-app", ".zip");
            Files.write(path, bytes);
            // step2: import objects
            if (!ctx.isDryRun()) {
                importArtifact(path, ctx.getTeamId(), ctx.getUserId(), outputs);
            }
        } catch (IOException e) {
            throw new MetaTaskException("Error creating temp file " + e.getMessage());
        } finally {
            deleteTempFile(path);
        }
    }

    // After processing the file, you can delete it using:
    private void deleteTempFile(Path tempFile) throws MetaTaskException {
        try {
            Files.deleteIfExists(tempFile);
        } catch (IOException e) {
            throw new MetaTaskException("Failed to delete temporary file: " + tempFile + " " + e.getMessage());
        }
    }

    private Manifest getManifest(Path path) {
        try (ZipInputStream zis = new ZipInputStream(Files.newInputStream(path))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if ("manifest.json".equals(entry.getName())) {
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = zis.read(buffer)) > 0) {
                        baos.write(buffer, 0, len);
                    }
                    byte[] entireContent = baos.toByteArray();
                    String json = new String(entireContent, StandardCharsets.UTF_8);
                    return ObjectJsonUtil.MAPPER.readValue(json, Manifest.class);
                }
            }
            return null;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void importArtifact(Path path, Long teamId, Long userId, List<String> outputs) {
        Manifest manifest = getManifest(path);
        if (manifest != null) {
            outputs.add("Manifest:");
            outputs.add(String.format("- version: %s", manifest.getVersion()));
            outputs.add(String.format("  snapshotOid: %s", manifest.getSnapshotOid()));
            outputs.add("");
            outputs.add("Library:");
            importLibrary(path, outputs);
            outputs.add("");
            outputs.add("Objects:");
        }
        ImportStatistic res = importObjects(path, teamId, userId, manifest);
        outputs.add(String.format("- total: %d", res.total));
        outputs.add(String.format("  alreadyExist: %d", res.alreadyExist));
        outputs.add(String.format("  newCreated: %d", res.newCreated));
        outputs.add(String.format("  badOid: %d", res.badOid));
    }

    private String extractOidFromFileName(String fileName, boolean v2) {
        if (v2 && !fileName.startsWith("objects/")) {
            return null;
        }
        if (!fileName.endsWith(".json")) {
            return null;
        }
        int start = v2 ? "objects/".length() : 0;
        return fileName.substring(start, fileName.lastIndexOf('.'));
    }

    private String extractOSSObjectNameFromFileName(String fileName) {
        if (!fileName.startsWith("library/")) {
            return null;
        }
        return fileName.substring("library/".length());
    }

    private String probeContentType(String fileName) {
        return URLConnection.guessContentTypeFromName(fileName);
    }

    private void importLibrary(Path path, List<String> outputs) {
        ByteArrayOutputStream baos = null;
        ByteArrayInputStream bais = null;
        try (ZipInputStream zis = new ZipInputStream(Files.newInputStream(path))) {
            ZipEntry entry = zis.getNextEntry();
            while (entry != null) {
                String fileName = entry.getName();
                String objectName = extractOSSObjectNameFromFileName(fileName);
                if (objectName == null) {
                    entry = zis.getNextEntry();
                    continue;
                }

                baos = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int len;
                while ((len = zis.read(buffer)) > 0) {
                    baos.write(buffer, 0, len);
                }
                baos.flush();
                bais = new ByteArrayInputStream(baos.toByteArray());

                boolean isPrivate = objectName.startsWith(CONSOLE_FILE_PREFIX + PRIVATE_PREFIX);
                String contentType = probeContentType(objectName);
                String uri = ossService.migrateFileIn(objectName, bais, contentType, isPrivate);
                outputs.add("- imported: " + uri);
                outputs.add("  contentType: " + contentType);
                entry = zis.getNextEntry();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (baos != null) {
                    baos.close();
                }
                if (bais != null) {
                    bais.close();
                }
            } catch (Exception e) {
                throw new TrantorRuntimeException(e);
            }
        }
    }

    private ImportStatistic importObjects(Path path, Long teamId, Long userId, Manifest manifest) {
        ImportStatistic res = new ImportStatistic();
        List<MetaObjectV2> saveItems = new ArrayList<>();
        ObjectUtil.traverseObjects(path, manifest, (oid, obj) -> {
            saveItems.add(obj);
            if (saveItems.size() >= 1000) {
                doBatchSave(teamId, saveItems, res);
                saveItems.clear();
            }
        });
        doBatchSave(teamId, saveItems, res);
        return res;
    }

    private void doBatchSave(Long teamId, List<MetaObjectV2> saveItems, ImportStatistic res) {
        if (saveItems.isEmpty()) {
            return;
        }
        int total = saveItems.size();
        int created = metaObjectRepo.save(teamId, saveItems);
        int alreadyExist = total - created;
        res.total += total;
        res.newCreated += created;
        res.alreadyExist += alreadyExist;
    }

    static class ImportStatistic {
        private int total = 0;
        private int alreadyExist = 0;
        private int newCreated = 0;
        private int badOid = 0;
    }
}
